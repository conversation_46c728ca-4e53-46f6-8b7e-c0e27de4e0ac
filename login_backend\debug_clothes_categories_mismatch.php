<?php
/**
 * 调试衣物数据和分类数据不匹配的问题
 * 检查圈子中的衣物使用了哪些分类，以及这些分类是否存在于分类表中
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    $result['user_id'] = $userId;
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("
        SELECT circle_id FROM circle_members 
        WHERE user_id = :user_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $result['user_circles'] = $userCircles;
    
    if (empty($userCircles)) {
        $result['message'] = '用户不在任何圈子中';
        echo json_encode(['status' => 'success', 'data' => $result], JSON_PRETTY_PRINT);
        exit;
    }
    
    $circleIds = implode(',', $userCircles);
    
    // 2. 检查圈子中的衣物数据及其分类
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.category, c.user_id, c.circle_id,
               u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id IN ($circleIds)
        ORDER BY c.user_id, c.category
    ");
    $stmt->execute();
    $circleClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['circle_clothes'] = [
        'count' => count($circleClothes),
        'clothes' => array_slice($circleClothes, 0, 10) // 只显示前10条作为示例
    ];
    
    // 3. 统计圈子中衣物使用的分类
    $stmt = $conn->prepare("
        SELECT category, COUNT(*) as count, 
               GROUP_CONCAT(DISTINCT user_id) as user_ids,
               GROUP_CONCAT(DISTINCT u.nickname) as creator_nicknames
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id IN ($circleIds) AND c.category IS NOT NULL AND c.category != ''
        GROUP BY category
        ORDER BY count DESC
    ");
    $stmt->execute();
    $clothesCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['clothes_categories_used'] = [
        'count' => count($clothesCategories),
        'categories' => $clothesCategories
    ];
    
    // 4. 检查这些分类是否存在于分类表中
    $usedCategoryNames = array_column($clothesCategories, 'category');
    $categoryChecks = [];
    
    foreach ($usedCategoryNames as $categoryName) {
        // 检查是否存在对应的分类定义
        $stmt = $conn->prepare("
            SELECT id, user_id, name, code, is_system, circle_id,
                   CASE WHEN circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories
            WHERE code = :category_code
        ");
        $stmt->bindParam(':category_code', $categoryName, PDO::PARAM_STR);
        $stmt->execute();
        $categoryDefs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $categoryChecks[$categoryName] = [
            'used_in_clothes' => true,
            'definitions_found' => count($categoryDefs),
            'definitions' => $categoryDefs
        ];
    }
    
    $result['category_definitions_check'] = $categoryChecks;
    
    // 5. 检查分类表中的所有分类（当前用户可见的）
    $stmt = $conn->prepare("
        SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at, c.circle_id,
               u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE (
            -- 当前用户的系统分类
            (c.is_system = 1 AND c.user_id = :user_id) OR
            -- 所有自定义分类：个人的 + 圈子共享的
            (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR 
             (c.circle_id IS NOT NULL AND c.circle_id IN ($circleIds))))
        )
        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $availableCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['available_categories'] = [
        'count' => count($availableCategories),
        'categories' => $availableCategories
    ];
    
    // 6. 检查是否有分类定义缺失
    $availableCategoryCodes = array_column($availableCategories, 'code');
    $missingCategories = array_diff($usedCategoryNames, $availableCategoryCodes);
    
    $result['missing_category_definitions'] = [
        'count' => count($missingCategories),
        'categories' => $missingCategories
    ];
    
    // 7. 检查圈子中是否有其他用户创建的自定义分类
    $stmt = $conn->prepare("
        SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.circle_id,
               u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.circle_id IN ($circleIds) AND c.user_id != :user_id
        ORDER BY c.created_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $otherUsersCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['other_users_categories'] = [
        'count' => count($otherUsersCategories),
        'categories' => $otherUsersCategories
    ];
    
    // 8. 分析问题
    $analysis = [];
    
    if (count($circleClothes) > 0 && count($otherUsersCategories) == 0) {
        $analysis[] = "圈子中有其他用户的衣物，但没有其他用户的自定义分类定义";
        $analysis[] = "可能原因：其他用户的衣物使用的是系统分类，或者分类没有同步到圈子";
    }
    
    if (count($missingCategories) > 0) {
        $analysis[] = "发现衣物使用了不存在的分类代码：" . implode(', ', $missingCategories);
        $analysis[] = "这些分类可能需要创建或同步到圈子";
    }
    
    if (count($clothesCategories) > count($availableCategories)) {
        $analysis[] = "衣物使用的分类数量多于可用的分类定义";
        $analysis[] = "可能存在分类同步问题";
    }
    
    $result['analysis'] = $analysis;
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
