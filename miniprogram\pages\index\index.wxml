<!-- 判断是否登录，显示不同的内容 -->
<block wx:if="{{hasUserInfo}}">
  <view class="container">
    <!-- 顶部标题栏 
    <view class="title-bar">
      <view class="title">我的衣橱</view>
    </view>-->
    
    <!-- 衣橱选择器 -->
    <view class="wardrobe-switcher">
      <view class="selected-wardrobe" bindtap="toggleWardrobePopup">
        <text>{{selectedWardrobeId === null ? '衣橱' : selectedWardrobeName}}</text>
        <view class="dropdown-icon"></view>
      </view>
              <!-- 新增：数据源切换按钮 -->
        <view class="data-source-btn" bindtap="toggleDataSourcePopup">
          <text>{{dataSourceOptions[dataSource === 'personal' ? 0 : dataSource === 'shared' ? 1 : 2].name}}</text>
          <view class="data-source-dropdown-icon"></view>
        </view>
      <view class="wardrobe-controls">

        <view class="layout-switch-btn" bindtap="switchLayoutMode">
          <view class="layout-icon layout-{{layoutMode}}"></view>
        </view>
        <view class="name-toggle {{showClothingName ? 'name-toggle-active' : ''}}" bindtap="toggleClothingName">
          <text>Aa</text>
        </view>
        <view class="search-btn" bindtap="toggleSearchPopup">
          <view class="search-icon"></view>
        </view>
      </view>
    </view>
    
    <!-- 可滚动内容区域 -->
    <view class="scrollable-content">
      <!-- 推荐模块容器 -->
      <view class="recommendation-container">
        <!-- 两大功能模块 -->
        <view class="feature-modules">
          <!-- 个人形象分析模块 -->
          <view class="feature-module feature-module-left" bindtap="togglePersonalAnalysisOptions">
            <image class="feature-icon" src="/images/xingxiangsheji.png" mode="aspectFit"></image>
            <view class="feature-info">
              <view class="feature-title">形象分析</view>
              <view class="feature-desc">面容、全身与穿搭分析</view>
            </view>
          </view>
          
          <!-- AI智能穿搭模块 -->
          <view class="feature-module feature-module-right" bindtap="toggleOutfitOptions">
            <image class="feature-icon" src="/images/aicd.png" mode="aspectFit"></image>
            <view class="feature-info">
              <view class="feature-title">智能穿搭</view>
              <view class="feature-desc">按天气与个人喜好推荐</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 分类选项卡 -->
      <view class="category-tabs-container">
        <view class="category-tabs">
          <view class="tab-item {{currentCategory === 'all' ? 'active' : ''}}" bindtap="switchCategory" data-category="all">全部 <text wx:if="{{currentCategory === 'all' && !isLoadingCategory && categoryCountMap['all'] > 0}}" class="tab-count"> {{categoryCountMap['all']}} 件</text></view>
          <view wx:for="{{categories}}" wx:key="value" class="tab-item {{currentCategory === item.value ? 'active' : ''}}" bindtap="switchCategory" data-category="{{item.value}}">{{item.name}} <text wx:if="{{currentCategory === item.value && !isLoadingCategory && categoryCountMap[item.value] > 0}}" class="tab-count"> {{categoryCountMap[item.value]}} 件</text></view>
        </view>
      </view>
      
      <!-- 衣物容器 -->
      <view class="clothes-container">
        <view class="clothes-grid layout-mode-{{layoutMode}}">
          <block wx:if="{{clothes.length > 0}}">
            <view class="clothes-item {{!showClothingName ? 'no-name' : ''}}" wx:for="{{clothes}}" wx:key="id" bindtap="viewClothingDetail" data-id="{{item.id}}">
              <image src="{{item.imageUrl}}" mode="aspectFit" class="clothing-image"></image>
              <!-- 新增：数据源标识 -->
              <view class="data-source-badge" wx:if="{{item.data_source === 'shared'}}">
                <text class="badge-text">共享</text>
              </view>
              <!-- 显示衣物名称 -->
              <view class="clothes-name" wx:if="{{showClothingName && item.name}}">{{item.name}}</view>
              <!-- 新增：创建者信息 -->
              <view class="creator-info" wx:if="{{item.data_source === 'shared' && item.creator_nickname}}">
                <text class="creator-name">{{item.creator_nickname}}</text>
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
    
    <!-- 体验账号提示 -->
    <view wx:if="{{isUsingMockUser}}" class="mock-user-banner">
      <text class="mock-banner-text">当前为体验账号数据，推荐登入后使用 →</text>
      <view class="mock-login-btn" bindtap="goToLogin">登录</view>
    </view>
  </view>
</block>

<!-- 未登录状态 -->
<block wx:else>
  <view class="container">
    
    <!-- 可滚动内容区域 -->
    <view class="scrollable-content">
      <!-- 推荐模块容器 -->
      <view class="recommendation-container">
        <!-- 两大功能模块 -->
        <view class="feature-modules">
          <!-- 个人形象分析模块 -->
          <view class="feature-module feature-module-left" bindtap="goToImageAnalysis">
            <image class="feature-icon" src="/images/xingxiangsheji.png" mode="aspectFit"></image>
            <view class="feature-info">
              <view class="feature-title">个人形象分析</view>
              <view class="feature-desc">面部、图片与穿搭分析</view>
            </view>
          </view>
          
          <!-- AI智能穿搭模块 -->
          <view class="feature-module feature-module-right" bindtap="goToSmartOutfit">
            <image class="feature-icon" src="/images/aicd.png" mode="aspectFit"></image>
            <view class="feature-info">
              <view class="feature-title">AI智能穿搭</view>
              <view class="feature-desc">按天气与个人喜好推荐</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 分类选项卡 -->
      <view class="category-tabs-container">
        <view class="category-tabs">
          <view class="tab-item active">全部</view>
          <view wx:for="{{categories}}" wx:key="value" class="tab-item">{{item.name}}</view>
        </view>
      </view>
      
      <!-- 衣物容器 -->
      <view class="clothes-container">
        <view class="empty-state">
          <view class="empty-icon">
            <image src="/images/emtpy.png" mode="aspectFit" class="empty-image"></image>
          </view>
          <view class="empty-text">您还没有添加任何衣物</view>
          <view class="empty-text text-sm">登录后可以开始管理您的次元衣帽间</view>
          <button class="login-btn" bindtap="goToLogin">
            立即登录
          </button>
        </view>
      </view>
    </view>
  </view>
</block>

<!-- 公告弹窗 -->
<view class="announcement-modal" wx:if="{{showAnnouncement}}" style="z-index: 1000;">
  <view class="announcement-overlay" bindtap="closeAnnouncement" style="z-index: 999;"></view>
  <view class="announcement-content">
    <view class="announcement-title">{{announcement.title}}</view>
    <text class="announcement-body">{{announcement.content}}</text>
    <view class="announcement-btn" bindtap="closeAnnouncement">我知道了</view>
  </view>
</view>

<!-- WXS 模块用于格式化时间 -->
<wxs module="wxs">
  function formatTime(timestamp) {
    var date = getDate(timestamp);
    var hours = date.getHours();
    var minutes = date.getMinutes();
    
    // 补齐0
    hours = hours < 10 ? '0' + hours : hours;
    minutes = minutes < 10 ? '0' + minutes : minutes;
    
    return hours + ':' + minutes;
  }
  
  module.exports = {
    formatTime: formatTime
  };
</wxs>

<!-- 添加衣物悬浮按钮 -->
<view class="add-btn {{isUsingMockUser ? 'add-btn-mock-user' : 'add-btn-logged-in'}}" bindtap="showUploadOptions" style="z-index: 100;" wx:if="{{!showAnnouncement}}">
  <text class="add-icon">+</text>
</view>

<!-- 上传选项弹出菜单 -->
<view class="upload-options-modal" wx:if="{{showUploadOptions}}">
  <view class="upload-options-overlay" bindtap="hideUploadOptions"></view>
  <view class="upload-options-content">
    <view class="upload-option-item" bindtap="singleUpload">
      <view class="upload-option-icon">📷</view>
      <view class="upload-option-text">单件上传</view>
    </view>
    <view class="upload-option-item" bindtap="batchUpload">
      <view class="upload-option-icon">📁</view>
      <view class="upload-option-text">批量上传</view>
    </view>
  </view>
</view>

<!-- 底部衣橱选择弹出框 -->
<view class="wardrobe-popup-mask" bindtap="closeWardrobePopup" wx:if="{{showWardrobePopup}}"></view>
<view class="wardrobe-popup {{showWardrobePopup ? 'wardrobe-popup-show' : ''}}">
  <view class="wardrobe-popup-header">
    <text class="wardrobe-popup-title">选择衣橱</text>
    <view class="wardrobe-popup-actions">
      <view class="wardrobe-popup-action" bindtap="goToAddWardrobe">
        <view class="action-icon add-icon-small"></view>
      </view>
      <view class="wardrobe-popup-action" bindtap="goToManageWardrobes">
        <view class="action-icon manage-icon-small"></view>
      </view>
      <view class="wardrobe-popup-close" bindtap="closeWardrobePopup">×</view>
    </view>
  </view>
  <scroll-view class="wardrobe-popup-content" scroll-y>
    <view class="wardrobe-popup-item {{selectedWardrobeId === null ? 'active' : ''}}" 
          bindtap="switchWardrobe" 
          data-id="{{null}}"
          data-name="全部衣橱">
      全部衣橱
    </view>
    <view wx:for="{{wardrobeList}}" 
          wx:key="id" 
          class="wardrobe-popup-item {{selectedWardrobeId == item.id ? 'active' : ''}}"
          bindtap="switchWardrobe"
          data-id="{{item.id}}"
          data-name="{{item.name}}">
      {{item.name}}
    </view>
  </scroll-view>
</view>

<!-- 新增：数据源选择弹出框 -->
<view class="data-source-popup-mask" bindtap="closeDataSourcePopup" wx:if="{{showDataSourcePopup}}"></view>
<view class="data-source-popup {{showDataSourcePopup ? 'data-source-popup-show' : ''}}">
  <view class="data-source-popup-header">
    <text class="data-source-popup-title">选择数据源</text>
    <view class="data-source-popup-close" bindtap="closeDataSourcePopup">×</view>
  </view>
  <view class="data-source-popup-content">
    <view wx:for="{{dataSourceOptions}}"
          wx:key="key"
          class="data-source-popup-item {{dataSource === item.key ? 'active' : ''}}"
          bindtap="switchDataSource"
          data-source="{{item.key}}">
      <text class="data-source-icon">{{item.icon}}</text>
      <text class="data-source-name">{{item.name}}</text>
    </view>
  </view>
</view>

<!-- 搜索弹出框 -->
<view class="search-popup-mask" bindtap="closeSearchPopup" wx:if="{{showSearchPopup}}"></view>
<view class="search-popup {{showSearchPopup ? 'search-popup-show' : ''}}">
  <view class="search-popup-header">
    <view class="search-input-container">
      <view class="search-icon-small"></view>
      <input class="search-input" type="text" placeholder="搜索衣物名称" confirm-type="search" bindinput="onSearchInput" bindconfirm="searchClothes" value="{{searchKeyword}}" focus="{{showSearchPopup}}" />
      <view class="search-clear-btn" bindtap="clearSearch" wx:if="{{searchKeyword}}">×</view>
    </view>
    <view class="search-popup-close" bindtap="closeSearchPopup">取消</view>
  </view>
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="search-result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="viewClothingDetail" data-id="{{item.id}}">
      <image class="search-result-image" src="{{item.imageUrl}}" mode="aspectFit"></image>
      <view class="search-result-info">
        <view class="search-result-name">{{item.name}}</view>
        <view class="search-result-category" wx:if="{{item.categoryName}}">{{item.categoryName}}</view>
      </view>
    </view>
  </view>
  <view class="search-empty" wx:elif="{{searchKeyword && searchResults.length === 0}}">
    <view class="search-empty-icon">🔍</view>
    <view class="search-empty-text">未找到相关衣物</view>
  </view>
  <view class="search-tips" wx:else>
    <view class="search-tip-text">输入衣物名称关键词搜索</view>
  </view>
</view> 

<!-- 智能穿搭选项弹出菜单 -->
<view class="outfit-options-modal" wx:if="{{showOutfitOptions}}">
  <view class="outfit-options-overlay" bindtap="closeOutfitOptions"></view>
  <view class="outfit-options-content">
    <view class="outfit-options-title">AI智能穿搭</view>
    <view class="outfit-option-item" bindtap="navigateToOutfitType" data-type="weather">
      <view class="outfit-option-icon">🌤️</view>
      <view class="outfit-option-info">
        <view class="outfit-option-name">按天气推荐</view>
        <view class="outfit-option-desc">根据当前天气推荐穿搭</view>
      </view>
    </view>
    <view class="outfit-option-item" bindtap="navigateToOutfitType" data-type="clothing">
      <view class="outfit-option-icon">👚</view>
      <view class="outfit-option-info">
        <view class="outfit-option-name">按衣物推荐</view>
        <view class="outfit-option-desc">基于指定衣物搭配整体造型</view>
      </view>
    </view>
    <view class="outfit-option-item" bindtap="navigateToOutfitType" data-type="preference">
      <view class="outfit-option-icon">❤️</view>
      <view class="outfit-option-info">
        <view class="outfit-option-name">按个人喜好推荐</view>
        <view class="outfit-option-desc">根据个人风格偏好推荐搭配</view>
      </view>
    </view>
    <view class="outfit-option-item" bindtap="navigateToOutfitType" data-type="analysis">
      <view class="outfit-option-icon">👩</view>
      <view class="outfit-option-info">
        <view class="outfit-option-name">按形象分析推荐</view>
        <view class="outfit-option-desc">基于个人形象特点推荐搭配</view>
      </view>
    </view>
  </view>
</view>

<!-- 个人形象分析选项弹出菜单 -->
<view class="personal-analysis-modal" wx:if="{{showPersonalAnalysisOptions}}">
  <view class="personal-analysis-overlay" bindtap="closePersonalAnalysisOptions"></view>
  <view class="personal-analysis-content">
    <view class="personal-analysis-title">个人形象分析</view>
    <view class="personal-analysis-item" bindtap="navigateToAnalysisType" data-type="image">
      <view class="personal-analysis-icon">✨</view>
      <view class="personal-analysis-info">
        <view class="personal-analysis-name">形象分析</view>
        <view class="personal-analysis-desc">全面分析您的气质特点、形象优势，提供专业穿搭建议</view>
      </view>
    </view>
    <view class="personal-analysis-item" bindtap="navigateToAnalysisType" data-type="outfit">
      <view class="personal-analysis-icon">👗</view>
      <view class="personal-analysis-info">
        <view class="personal-analysis-name">穿搭分析</view>
        <view class="personal-analysis-desc">评估整体搭配协调性、时尚度，提供改进建议与风格提升方案</view>
      </view>
    </view>
    <view class="personal-analysis-item" bindtap="navigateToAnalysisType" data-type="face">
      <view class="personal-analysis-icon">👩</view>
      <view class="personal-analysis-info">
        <view class="personal-analysis-name">面容分析</view>
        <view class="personal-analysis-desc">分析面部特征与妆容效果，推荐适合您的化妆技巧与方案</view>
      </view>
    </view>
  </view>
</view> 