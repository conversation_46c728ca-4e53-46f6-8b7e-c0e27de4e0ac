const app = getApp();

Page({
  data: {
    photos: [],
    selectedPhotoId: null,
    loading: true,
    mode: '', // 用于区分不同的使用场景，'try_on'表示试穿模式
    canProceed: false,
    clothesId: null,
    merchantId: null,
    selectMode: false
  },

  onLoad: function(options) {
    console.log("照片选择页面接收到的参数:", options);
    
    // 初始化数据
    this.setData({
      mode: '',
      clothesId: null,
      merchantId: null,
      selectMode: false
    });
    
    // 检查是否有传入模式参数
    if (options && options.mode) {
      this.setData({
        mode: options.mode
      });
    }
    
    // 检查是否是从衣物详情页进入的选择模式
    if (options && options.select_mode === 'true') {
      this.setData({
        selectMode: true,
        mode: 'try_on' // 设置为试穿模式
      });
      
      // 如果传入了clothes_id参数，保存衣物ID
      if (options.clothes_id) {
        this.setData({
          clothesId: options.clothes_id
        });
        console.log("从衣物详情页进入，衣物ID:", options.clothes_id);
      }
      
      // 如果传入了商家ID，保存商家ID
      if (options.merchant_id) {
        this.setData({
          merchantId: options.merchant_id
        });
        console.log("从衣物详情页进入，商家ID:", options.merchant_id);
      }
    }
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '选择照片'
    });
    
    // 加载照片
    this.loadPhotos();
  },
  
  // 加载用户照片
  loadPhotos: function() {
    wx.showLoading({
      title: '加载中...'
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_photos.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 添加照片类型（根据后端返回的type字段判断）
          const photos = res.data.data.map(photo => {
            let photoType = "个人照";
            if (photo.type === 'full') {
              photoType = "全身照";
            } else if (photo.type === 'half') {
              photoType = "半身照";
            }
            
            // 确保照片URL字段一致性
            const photoUrl = photo.image_url || photo.url;
            
            return {
              ...photo,
              photoType: photoType,
              image_url: photoUrl, // 确保image_url字段存在
              isLoading: false,  // 默认不显示加载状态
              loadError: false,  // 默认没有加载错误
              width: 0,          // 初始宽度
              height: 0          // 初始高度
            };
          });
          
          this.setData({
            photos: photos,
            loading: false
          });
        } else {
          this.setData({
            photos: [],
            loading: false
          });
          
          wx.showToast({
            title: res.data.message || '获取照片失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        
        this.setData({
          photos: [],
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  },
  
  // 选择照片
  selectPhoto: function(e) {
    const photoId = e.currentTarget.dataset.id;
    
    this.setData({
      selectedPhotoId: photoId,
      canProceed: true
    });
  },
  
  // 预览图片
  previewImage: function(e) {
    // 修改事件冒泡处理方式，因为某些情况下e.stopPropagation可能不存在
    // 使用catch:tap在wxml中已经阻止了冒泡，这里不需要再调用stopPropagation
    
    const url = e.currentTarget.dataset.url;
    // 防止没有url的情况
    if (!url) {
      console.error('预览图片失败：图片URL为空');
      return;
    }
    
    wx.previewImage({
      urls: [url],
      current: url
    });
  },
  
  // 图片加载错误处理
  imageLoadError: function(e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.photos;
    
    // 确保index有效
    if (index >= 0 && index < photos.length) {
      photos[index].loadError = true;
      photos[index].isLoading = false; // 确保loading状态关闭
      
      this.setData({
        photos: photos
      });
    }
  },
  
  // 图片加载完成处理
  imageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.photos;
    
    // 确保index有效
    if (index >= 0 && index < photos.length) {
      // 获取图片实际宽高
      const {width, height} = e.detail;
      photos[index].isLoading = false;
      photos[index].width = width;
      photos[index].height = height;
      
      this.setData({
        photos: photos
      });
    }
  },
  
  // 继续下一步，开始试穿
  proceedToTryOn: function() {
    if (!this.data.canProceed) {
      wx.showToast({
        title: '请先选择一张照片',
        icon: 'none'
      });
      return;
    }
    
    // 查找选中的照片
    const selectedPhoto = this.data.photos.find(photo => photo.id === this.data.selectedPhotoId);
    
    if (!selectedPhoto) {
      wx.showToast({
        title: '照片数据错误',
        icon: 'none'
      });
      return;
    }
    
    // 如果是试穿模式，设置全局选中照片并跳转到试穿结果页
    if (this.data.mode === 'try_on') {
      // 设置全局变量中的选中照片
      app.globalData.selectedTryOnPhoto = selectedPhoto;
      
      // 从衣物详情页进入的情况
      if (this.data.selectMode && this.data.clothesId) {
        console.log("从衣物详情页进入试穿模式，获取衣物详情");
        this.getClothingDetails(this.data.clothesId, selectedPhoto);
        return;
      }
      
      // 检查是否已选择衣物
      if (!app.globalData.selectedClothes || app.globalData.selectedClothes.length === 0) {
        wx.showToast({
          title: '请先选择衣物',
          icon: 'none'
        });
        return;
      }
      
      // 开始试穿流程
      this.startTryOn(selectedPhoto, app.globalData.selectedClothes);
    } else {
      // 其他模式的处理
      wx.navigateBack({
        delta: 1
      });
    }
  },
  
  // 获取衣物详情并进行试穿
  getClothingDetails: function(clothingId, selectedPhoto) {
    wx.showLoading({
      title: '准备试穿...'
    });
    
    // 构建请求URL
    let url = `${app.globalData.apiBaseUrl}/get_clothes.php?id=${clothingId}`;
    
    // 智能处理商家ID - 只有当确定是在商家场景下才添加merchantId参数
    const isFromMerchant = this.data.merchantId && parseInt(this.data.merchantId, 10) > 0;
    
    if (isFromMerchant) {
      url += `&merchant_id=${this.data.merchantId}`;
      console.log("商家模式获取衣物，商家ID:", this.data.merchantId);
    } else {
      console.log("用户模式获取衣物，不添加商家ID");
    }
    
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          // 获取衣物详情
          const clothing = res.data.data[0];
          console.log("获取到的衣物详情:", clothing);
          
          // 检查是否为商家衣物
          const isMerchantClothing = clothing.merchant && clothing.merchant.id || clothing.is_merchant_clothes === true;
          console.log("衣物所有权判断:", isMerchantClothing ? "商家衣物" : "用户自己的衣物");
          
          // 验证衣物图片URL
          if (!this.validateImageUrl(clothing.image_url)) {
            wx.showModal({
              title: '图片错误',
              content: '衣物图片URL无效或不完整，无法进行试穿',
              showCancel: false,
              success: () => {
                wx.navigateBack();
              }
            });
            return;
          }
          
          // 验证用户照片URL
          if (!this.validateImageUrl(selectedPhoto.image_url)) {
            wx.showModal({
              title: '图片错误',
              content: '照片图片URL无效或不完整，无法进行试穿',
              showCancel: false,
              success: () => {
                wx.navigateBack();
              }
            });
            return;
          }
          
          // 确保图片URL是完整的绝对路径
          if (clothing.image_url && !clothing.image_url.startsWith('http')) {
            clothing.image_url = app.globalData.apiBaseUrl + '/' + clothing.image_url.replace(/^\//, '');
          }
          
          // 确保照片URL是完整的绝对路径
          if (selectedPhoto.image_url && !selectedPhoto.image_url.startsWith('http')) {
            selectedPhoto.image_url = app.globalData.apiBaseUrl + '/' + selectedPhoto.image_url.replace(/^\//, '');
          }
          
          console.log("试穿使用的图片URLs - 衣物:", clothing.image_url, "照片:", selectedPhoto.image_url);
          
          // 构建试穿所需的衣物数组
          app.globalData.selectedClothes = [clothing];
          app.globalData.selectedTryOnPhoto = selectedPhoto;
          
          // 智能处理商家ID
          if (isMerchantClothing && clothing.merchant && clothing.merchant.id) {
            // 如果是商家衣物且有商家信息，保存到全局状态
            app.globalData.selectedMerchantId = clothing.merchant.id;
            console.log("保存商家ID到全局状态(来自衣物数据):", clothing.merchant.id);
          } else if (isFromMerchant) {
            // 如果从商家页面进入且没有衣物商家信息，使用页面商家ID
            app.globalData.selectedMerchantId = parseInt(this.data.merchantId, 10);
            console.log("保存商家ID到全局状态(来自页面参数):", this.data.merchantId);
          } else {
            // 如果不是商家衣物，清除商家ID
            app.globalData.selectedMerchantId = null;
            console.log("清除全局商家ID状态，试穿用户自己的衣物");
          }
          
          // 预加载图片以验证其有效性
          this.preloadImages([clothing.image_url, selectedPhoto.image_url], () => {
            // 开始试穿流程
            this.startTryOn(selectedPhoto, app.globalData.selectedClothes);
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取衣物详情失败',
            icon: 'none'
          });
          
          console.error("获取衣物详情失败:", res.data);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        
        console.error("获取衣物详情网络错误:", err);
      }
    });
  },
  
  // 验证图片URL
  validateImageUrl: function(url) {
    if (!url || typeof url !== 'string') {
      console.error("图片URL无效:", url);
      return false;
    }
    
    // URL必须是非空字符串
    if (url.trim() === '') {
      console.error("图片URL为空");
      return false;
    }
    
    // 如果不是以http开头的绝对路径，也可以是相对路径
    // 这里我们只验证基本格式，实际有效性在preloadImages中检查
    return true;
  },
  
  // 预加载图片以验证其有效性
  preloadImages: function(urls, callback) {
    if (!urls || urls.length === 0) {
      callback();
      return;
    }
    
    let loadedCount = 0;
    let allValid = true;
    const processedUrls = [];
    
    wx.showLoading({
      title: '验证图片...'
    });
    
    urls.forEach((url, index) => {
      // 格式化URL，去除查询参数
      const cleanUrl = url.split('?')[0];
      
      // 使用小程序的getImageInfo API来验证图片
      wx.getImageInfo({
        src: cleanUrl,
        success: (res) => {
          console.log(`图片${index+1}加载成功:`, res.width, 'x', res.height, '格式:', res.type);
          
          // 确保图片格式是JPG、PNG或WEBP
          if (res.type && ['jpg', 'jpeg', 'png', 'webp'].includes(res.type.toLowerCase())) {
            processedUrls.push({
              url: cleanUrl,
              width: res.width,
              height: res.height,
              type: res.type
            });
          } else {
            console.error(`图片${index+1}格式不支持:`, res.type);
            allValid = false;
          }
          
          loadedCount++;
          
          if (loadedCount === urls.length) {
            completeVerification();
          }
        },
        fail: (err) => {
          console.error(`图片${index+1}加载失败:`, err);
          loadedCount++;
          allValid = false;
          
          if (loadedCount === urls.length) {
            completeVerification();
          }
        }
      });
    });
    
    // 完成验证处理
    const completeVerification = () => {
      wx.hideLoading();
      
      if (allValid && processedUrls.length === urls.length) {
        console.log("所有图片验证通过，尺寸信息:", processedUrls);
        // 更新全局图片数据
        if (app.globalData.selectedClothes && app.globalData.selectedClothes.length > 0) {
          app.globalData.selectedClothes[0]._image_info = processedUrls[0];
        }
        if (app.globalData.selectedTryOnPhoto) {
          app.globalData.selectedTryOnPhoto._image_info = processedUrls[1];
        }
        callback();
      } else {
        wx.showModal({
          title: '图片错误',
          content: '一个或多个图片无法正常加载或格式不支持，请确保图片是JPG、PNG或WEBP格式',
          showCancel: false
        });
      }
    };
  },
  
  // 上传新照片
  uploadPhoto: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        wx.showLoading({
          title: '上传中...'
        });

        // 获取当前token
        const token = app.globalData.token;
        console.log("上传照片使用的token:", token);
        
        wx.uploadFile({
          url: `${app.globalData.apiBaseUrl}/upload_photo.php`,
          filePath: tempFilePath,
          name: 'image',
          header: {
            'Authorization': token  // 在header中设置token
          },
          formData: {
            'token': token,   // 保留formData中的token作为备用
            'type': 'full'    // 假设上传的是全身照
          },
          success: (uploadRes) => {
            wx.hideLoading();
            
            console.log("上传照片响应:", uploadRes);
            
            try {
              const result = JSON.parse(uploadRes.data);
              
              if (!result.error) {
                wx.showToast({
                  title: '上传成功',
                  icon: 'success'
                });
                
                // 重新加载照片列表
                this.loadPhotos();
              } else {
                // 显示详细错误信息
                console.error("上传失败:", result);
                wx.showToast({
                  title: result.msg || result.message || '上传失败',
                  icon: 'none'
                });
              }
            } catch (e) {
              console.error("解析上传响应失败:", e, uploadRes.data);
              wx.showToast({
                title: '解析响应失败',
                icon: 'none'
              });
            }
          },
          fail: (error) => {
            wx.hideLoading();
            
            console.error("上传照片请求失败:", error);
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      }
    });
  },
  
  // 开始试穿流程
  startTryOn: function(photo, clothes) {
    wx.showLoading({
      title: '准备试穿...',
      mask: true
    });
    
    // 构建跳转URL
    let url = '/pages/try_on/start/index';
    
    // 检查是否为商家衣物
    const isMerchantClothing = 
      (clothes && clothes[0] && clothes[0].merchant && clothes[0].merchant.id) || 
      (clothes && clothes[0] && clothes[0].is_merchant_clothes === true) ||
      (this.data.merchantId && parseInt(this.data.merchantId, 10) > 0);
      
    // 只有在确认是商家衣物时，才传递merchant_id参数
    if (isMerchantClothing) {
      // 确定使用哪个商家ID (优先级: 衣物数据 > 页面数据)
      let merchantId = null;
      
      if (clothes && clothes[0] && clothes[0].merchant && clothes[0].merchant.id) {
        merchantId = clothes[0].merchant.id;
        console.log("通过URL参数传递商家ID(来自衣物数据):", merchantId);
      } else if (this.data.merchantId) {
        merchantId = this.data.merchantId;
        console.log("通过URL参数传递商家ID(来自页面参数):", this.data.merchantId);
      }
      
      if (merchantId) {
        url += `?merchant_id=${merchantId}`;
      }
    } else {
      console.log("试穿用户自己的衣物，不传递商家ID参数");
    }
    
    // 开始试穿流程，跳转到试穿结果页
    wx.navigateTo({
      url: url,
      success: () => {
        wx.hideLoading();
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
}); 