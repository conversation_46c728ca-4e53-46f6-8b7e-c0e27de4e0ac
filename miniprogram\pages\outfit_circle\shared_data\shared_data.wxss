/* 圈子数据共享页面样式 */
/* 模块4：数据共享基础模块 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 页面头部 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
}

.circle-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.circle-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-right: 20rpx;
}

.circle-role {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.stats-bar {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 数据同步区域 */
.sync-section {
  background-color: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.sync-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.sync-desc {
  font-size: 26rpx;
  color: #666;
}

.sync-actions {
  display: flex;
  justify-content: center;
}

.sync-btn {
  background-color: #333;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.sync-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  transition: all 0.3s;
}

.tab-item.active {
  color: #667eea;
  background-color: #f0f4ff;
  font-weight: 600;
}

/* 内容区域 */
.content-area {
  margin: 20rpx;
}

/* 过滤栏 */
.filter-bar {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-label {
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}

.filter-options {
  display: flex;
  gap: 20rpx;
}

.filter-option {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.filter-option.active {
  color: #667eea;
  background-color: #f0f4ff;
  font-weight: 500;
}

.category-scroll {
  flex: 1;
}

.category-options {
  display: flex;
  gap: 16rpx;
  white-space: nowrap;
}

.category-option {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-option.active {
  color: #667eea;
  background-color: #f0f4ff;
  font-weight: 500;
}

/* 衣橱列表 */
.wardrobes-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.wardrobe-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.wardrobe-item:last-child {
  border-bottom: none;
}

.wardrobe-item:active {
  background-color: #f8f8f8;
}

.wardrobe-info {
  flex: 1;
}

.wardrobe-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.wardrobe-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.wardrobe-badges {
  display: flex;
  gap: 8rpx;
}

.badge {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.shared-badge {
  background-color: #e8f5e8;
  color: #52c41a;
}

.own-badge {
  background-color: #fff7e6;
  color: #fa8c16;
}

.default-badge {
  background-color: #f0f4ff;
  color: #667eea;
}

.wardrobe-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.wardrobe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.creator {
  font-size: 24rpx;
  color: #999;
}

.clothes-count {
  font-size: 24rpx;
  color: #666;
}

.wardrobe-arrow {
  color: #ccc;
  font-size: 24rpx;
}

/* 衣物网格 */
.clothes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.clothes-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.clothes-item:active {
  transform: scale(0.98);
}

.clothes-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
}

.clothes-image {
  width: 100%;
  height: 100%;
}

.clothes-badges {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.clothes-info {
  padding: 20rpx;
}

.clothes-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clothes-category {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.clothes-creator {
  font-size: 20rpx;
  color: #999;
}

/* 穿搭网格 */
.outfits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.outfit-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.outfit-item:active {
  transform: scale(0.98);
}

.outfit-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  overflow: hidden;
  background-color: #f8f8f8;
}

.outfit-image {
  width: 100%;
  height: 100%;
}

/* 复用穿搭列表的样式 */
.outfit-placeholder {
  width: 100%;
  height: 100%;
  background-color: transparent;
  padding: 0;
  overflow: hidden;
}

/* 穿搭视图容器 */
.outfit-view-mini {
  width: 180px;
  height: 180px;
  position: relative;
  background-color: transparent;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* 迷你版穿搭衣物项 */
.outfit-item-mini {
  position: absolute;
  overflow: hidden;
  transform-origin: center center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 迷你版衣物图片 */
.item-image-mini {
  width: 95%;
  height: 95%;
  object-fit: contain;
  display: block;
  margin: auto;
}

/* 无衣物时的占位符 */
.outfit-no-items {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

.outfit-badges {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.outfit-clothes-count {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.outfit-info {
  padding: 20rpx;
}

.outfit-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.outfit-category {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.outfit-creator {
  font-size: 20rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 同步弹框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.sync-modal {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.modal-header {
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx 40rpx;
}

.modal-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.sync-options {
  margin-bottom: 30rpx;
}

.sync-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sync-option:last-child {
  border-bottom: none;
}

.sync-option checkbox {
  margin-right: 20rpx;
}

/* 修改checkbox选中颜色为黑色 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #333 !important;
  border-color: #333 !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  color: #fff !important;
}

.sync-option text {
  font-size: 28rpx;
  color: #333;
}

.sync-type-options {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.option-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.sync-type-option {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.sync-type-option radio {
  margin-right: 20rpx;
}

/* 修改radio选中颜色为黑色 */
radio .wx-radio-input.wx-radio-input-checked {
  background-color: #333 !important;
  border-color: #333 !important;
}

radio .wx-radio-input.wx-radio-input-checked::before {
  background-color: #fff !important;
}

.sync-type-option text {
  font-size: 26rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  background-color: transparent;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #667eea;
  font-weight: 600;
}

.confirm-btn[disabled] {
  color: #ccc;
}
