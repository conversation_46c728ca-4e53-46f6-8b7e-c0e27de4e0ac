/* pages/outfits/index/index.wxss */

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  height: auto;
  background-color: #f8f8f8;
  box-sizing: border-box;
  position: relative;
  padding: 0;
}

/* 顶部固定区域 */
.fixed-header {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 20; /* 确保在其他内容之上 */
  background-color: #f8f8f8;
}

/* 分类滚动栏 */
.categories-bar {
  width: 100%;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.categories-scroll {
  white-space: nowrap;
  padding: 20rpx 20rpx 16rpx;
  box-sizing: border-box;
}

.category-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f6f6f6;
  border-radius: 26rpx;
  transition: all 0.2s ease;
}

.category-item.active {
  color: #ffffff;
  background-color: #000000;
  font-weight: 500;
}

/* 页面标题 */
.page-title {
  font-size: 22px;
  font-weight: 600;
  padding: 20px 24px 10px;
  color: #333;
}

/* 穿搭列表容器 */
.outfits-container {
  flex: 1;
  padding: 24rpx 24rpx 70px; /* 使用固定的底部内边距，与首页保持一致 */
  box-sizing: border-box;
  overflow-y: auto; /* 添加垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  height: calc(100vh - 100px); /* 设置固定高度，减去头部和底部导航的高度 */
}

/* 穿搭网格布局 */
.outfits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

/* 穿搭项目 */
.outfit-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.outfit-item:active {
  transform: scale(0.97);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

/* 穿搭预览图 */
.outfit-preview {
  width: 100%;
  height: 180px;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 穿搭缩略图 */
.outfit-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 穿搭占位图 - 显示完整穿搭 */
.outfit-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #f9f9f9;
  background-image: radial-gradient(#f5f5f5 1px, transparent 1px);
  background-size: 10px 10px;
  box-sizing: border-box;
  padding: 0;
  overflow: hidden;
}

/* 穿搭视图容器 */
.outfit-view-mini {
  width: 180px;
  height: 180px;
  position: relative;
  background-color: transparent;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  /* 移除水平偏移，保持正常居中 */
}

/* 迷你版穿搭衣物项 */
.outfit-item-mini {
  position: absolute;
  overflow: hidden;
  transform-origin: center center;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 移除负margin，避免过度调整 */
}

/* 迷你版衣物图片 */
.item-image-mini {
  width: 95%;
  height: 95%;
  object-fit: contain;
  display: block;
  margin: auto;
}

/* 保留之前的样式但不再使用 */
.placeholder-image {
  max-width: 70%;
  max-height: 70%;
  object-fit: contain;
}

.placeholder-count {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 5;
}

/* 点赞数量显示 */
.like-count-overlay {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 5;
  display: flex;
  align-items: center;
}

.like-icon-small {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
  display: block;
  flex-shrink: 0;
}

.like-count-small {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 没有衣物的穿搭 */
.outfit-no-items {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  background-image: linear-gradient(45deg, #f5f5f5 25%, transparent 25%, transparent 75%, #f5f5f5 75%, #f5f5f5), 
                    linear-gradient(45deg, #f5f5f5 25%, transparent 25%, transparent 75%, #f5f5f5 75%, #f5f5f5);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.icon-outfit {
  font-size: 48px;
  color: #dddddd;
}

/* 穿搭信息 */
.outfit-info {
  padding: 16rpx 20rpx;
  background: linear-gradient(to bottom, #ffffff, #fafafa);
}

.outfit-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333333;
}

.outfit-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.outfit-date {
  font-size: 24rpx;
  color: #999999;
}

.outfit-like-info {
  display: flex;
  align-items: center;
  height: 28rpx;
}

/* 新增：创建者信息样式 */
.creator-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.creator-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.creator-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: rgba(255,255,255,0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80%;
  text-align: center;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 40rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.empty-icon {
  font-size: 80px;
  margin-bottom: 16px;
  color: #f0f0f0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333333;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
  line-height: 1.5;
  width: 90%;
}

.empty-action {
  margin-top: 30rpx;
  padding: 14rpx 30rpx;
  background-color: #f6f6f6;
  border-radius: 30rpx;
  color: #666;
  font-size: 26rpx;
}

.empty-action:active {
  background-color: #eee;
}

/* 加载更多 */
.loading-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin-top: 20rpx;
  height: 80rpx;
}

/* 手动加载更多按钮 */
.manual-load-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin: 30rpx 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  height: 88rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.manual-load-more:active {
  background-color: #f8f8f8;
}

.manual-load-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.loading-spinner-small {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 15rpx;
}

.loading-more-text {
  font-size: 28rpx;
  color: #999999;
}

.loading-end {
  text-align: center;
  padding: 30rpx 0 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 添加按钮 */
.add-btn {
  position: fixed !important;
  right: 30rpx !important;
  z-index: 1000 !important;
  background-color: #000000 !important;
  width: 100rpx !important;
  height: 100rpx !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
  /* 使用与首页相同的固定位置 */
  bottom: 80rpx !important; /* 固定位置，与首页保持一致 */
}

/* 穿搭广场按钮已移除，改为横幅形式 */

/* 体验账号状态下按钮位置 */
.add-btn-mock-user {
  bottom: 140rpx !important; /* 固定位置，与首页保持一致 */
}

/* 登录状态下按钮位置 */
.add-btn-logged-in {
  bottom: 80rpx !important; /* 固定位置，与首页保持一致 */
}

/* 广场按钮位置样式已移除 */

.add-icon {
  color: white !important;
  font-size: 50rpx !important;
  font-weight: bold !important;
  line-height: 1 !important; /* 确保行高为1，更好地控制垂直居中 */
  text-align: center !important; /* 水平居中 */
  height: 50rpx !important; /* 与字体大小一致 */
  display: flex !important; /* 使用flex布局 */
  justify-content: center !important; /* 水平居中 */
  align-items: center !important; /* 垂直居中 */
  padding-bottom: 4rpx !important; /* 微调垂直位置，视觉上更居中 */
  /* 确保没有被其他样式覆盖 */
  background-color: transparent !important;
  width: 50rpx !important; /* 明确宽度，与高度相等 */
  border-radius: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
}

/* 广场按钮图标样式已移除 */

/* AI智能穿搭推荐 */
.ai-recommendation {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 15; /* 确保在分类栏之上 */
}

.ai-recommendation:active {
  background-color: #f9f9f9;
}

.ai-recommendation-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.ai-icon-image {
  width: 70rpx;
  height: 70rpx;
}

.ai-recommendation-content {
  flex: 1;
}

.ai-recommendation-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.ai-recommendation-subtitle {
  font-size: 24rpx;
  color: #999;
}

.ai-recommendation-arrow {
  margin-left: 24rpx;
  color: #999;
  font-size: 24rpx;
}

/* 推荐穿搭入口 */
.recommended-block {
  margin: 20rpx 30rpx;
}

.recommended-banner {
  position: relative;
  height: 180rpx;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.banner-text {
  position: relative;
  z-index: 2;
  margin-left: 30rpx;
  color: #fff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

.banner-action {
  position: absolute;
  right: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  z-index: 2;
}

/* 穿搭广场横幅 */
.square-banner {
  margin: 20rpx 30rpx;
  padding: 24rpx 30rpx;
  background: linear-gradient(to right, #333333, #454545);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.square-banner:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.banner-content {
  flex: 1;
}

.banner-title {
  font-size: 34rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.banner-description {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.banner-arrow {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
}

.arrow-text {
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 6rpx;
}

.arrow-icon {
  color: #ffffff;
  font-size: 24rpx;
}

/* 底部安全区域占位 */
.safe-area-bottom {
  height: constant(safe-area-inset-bottom); /* iOS 11.2以下 */
  height: env(safe-area-inset-bottom); /* iOS 11.2+ */
  width: 100%;
} 

.outfit-private-tag {
  display: flex;
  align-items: center;
  height: 28rpx;
  background-color: #333;
  border-radius: 14rpx;
  padding: 2rpx 12rpx;
}

.private-tag-text {
  font-size: 22rpx;
  color: #fff;
  line-height: 1;
  display: flex;
  align-items: center;
} 

/* 分类选择器样式 */
.category-switcher {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.selected-category {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 36rpx;
  font-size: 26rpx;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
  margin: 0 8rpx 0 20rpx;
  flex-shrink: 0;
}

.selected-category:active {
  background-color: #f5f5f5;
}

.selected-category-dropdown-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #666;
  border-bottom: 3rpx solid #666;
  transform: rotate(45deg);
  margin-left: 16rpx;
  margin-top: -4rpx;
}

/* 新增：数据源切换按钮样式 */
.data-source-btn {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 36rpx;
  font-size: 26rpx;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
  margin: 0 20rpx 0 0;
  flex-shrink: 0;
}

.data-source-btn:active {
  background-color: #f5f5f5;
}



.data-source-dropdown-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #666;
  border-bottom: 3rpx solid #666;
  transform: rotate(45deg);
  margin-left: 16rpx;
  margin-top: -4rpx;
}

.dropdown-icon {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 10rpx solid #666;
  margin-left: 16rpx;
  margin-top: 4rpx;
  opacity: 0.8;
}

/* 搜索框样式 */
.outfit-search-box {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 14rpx 20rpx;
  flex: 1;
  margin-left: 30rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.outfit-search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
  opacity: 0.5;
  flex-shrink: 0;
}

.outfit-search-input {
  flex: 1;
  height: 28rpx;
  font-size: 26rpx;
  color: #333;
  padding: 0;
  line-height: 1;
}

.outfit-search-placeholder {
  color: #999;
  font-size: 26rpx;
}

.outfit-search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
  font-weight: normal;
  border-radius: 50%;
}

.outfit-search-clear:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 分类弹出框样式 */
.category-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1100;  /* 增加z-index值 */
}

.category-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1101;  /* 增加z-index值，保持比mask高 */
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.category-popup-show {
  transform: translateY(0);
}

.category-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.category-popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-popup-actions {
  display: flex;
  align-items: center;
}

.category-popup-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  position: relative;
}

.category-popup-action:active {
  opacity: 0.7;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.category-popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 0;
  max-height: 60vh;
  -webkit-overflow-scrolling: touch;
}

.category-popup-item {
  padding: 24rpx 30rpx;
  font-size: 30rpx;
  color: #333;
  position: relative;
  transition: all 0.2s ease;
}

.category-popup-item:active {
  background-color: #f5f5f5;
}

.category-popup-item.active {
  color: #000;
  font-weight: 500;
  background-color: #f8f8f8;
}

.category-popup-item.active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background-color: #000;
  border-radius: 0 4rpx 4rpx 0;
}

/* 新增：数据源弹出框样式 */
.data-source-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1100;
}

.data-source-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1101;
  max-height: 50vh;
}

.data-source-popup-show {
  transform: translateY(0);
}

.data-source-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.data-source-popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.data-source-popup-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.data-source-popup-content {
  padding: 20rpx 0;
}

.data-source-popup-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  cursor: pointer;
}

.data-source-popup-item:active {
  background-color: #f5f5f5;
}

.data-source-popup-item.active {
  background-color: #e3f2fd;
}

.data-source-popup-item .data-source-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
}

.data-source-popup-item .data-source-name {
  font-size: 32rpx;
  color: #333;
}

/* 添加按钮的z-index降低 */
.add-btn {
  z-index: 1000 !important; /* 确保低于弹出框 */
}

/* 双banner区域 */
.banner-section {
  margin: 20rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

/* banner项目样式 */
.banner-item {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  padding: 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  flex: 1;
  max-width: calc(50% - 16rpx);
  box-sizing: border-box;
}

.banner-item:active {
  transform: scale(0.98);
}

/* 左侧banner */
.banner-left {
  margin-right: 8rpx;
  margin-left: 16rpx;
}

/* 右侧banner */
.banner-right {
  margin-left: 8rpx;
  margin-right: 16rpx;
}

.banner-icon {
  width: 72rpx;
  height: 72rpx;
  margin-right: 16rpx;
  border-radius: 16rpx;
}

.banner-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 4rpx;
}

.banner-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  text-align: left;
}

.banner-description {
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  text-align: left;
}