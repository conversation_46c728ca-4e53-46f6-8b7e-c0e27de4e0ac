const app = getApp();

Page({
  data: {
    outfits: [],
    loading: true,
    isEmpty: false,
    categoryId: null,
    categoryName: '分类穿搭',
    categories: [],
    loadingCategories: false,
    currentCategoryIndex: -1,
    currentPage: 1,
    hasMoreData: true,
    loadingMore: false,
    scrollViewHeight: '',
    currentCategoryId: 'all',
    isRefreshing: false,
    hasUserInfo: false,
    fromCalendar: false, // 标记是否从日历页面跳转而来
    showCategoryPopup: false, // 添加：控制分类弹出框显示状态
    outfitSearchKeyword: '', // 添加：穿搭搜索关键词
    originalOutfits: [], // 添加：存储原始穿搭列表，用于搜索
    isSearchMode: false, // 添加：是否处于搜索模式

    // 新增：数据源相关
    dataSource: 'personal', // personal, shared, all
    showDataSourcePopup: false,
    dataSourceOptions: [
      { key: 'personal', name: '个人数据', icon: '👤' },
      { key: 'shared', name: '共享数据', icon: '👥' },
      { key: 'all', name: '全部数据', icon: '🌐' }
    ]
  },

  // 跳转到推荐穿搭列表
  goToRecommended() {
    wx.navigateTo({
      url: '/pages/recommended_outfits/index/index'
    });
  },

  // 跳转到共同管理衣橱页面
  navigateToCircle: function() {
    wx.navigateTo({
      url: '/pages/outfit_circle/index/index'
    });
  },

  onLoad: function(options) {
    // 检查是否从日历页面跳转而来
    if (options.fromCalendar === 'true') {
      this.setData({
        fromCalendar: true
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '选择穿搭'
      });
    }
    
    // 获取用户登录状态
    if (app.globalData.userInfo) {
      this.setData({
        hasUserInfo: true
      });
    }
    
    // 检查是否有分类ID，如果有，设置到data中
    if (options && options.category_id) {
      this.setData({
        categoryId: options.category_id,
        categoryName: options.category_name || '分类穿搭'
      });
      
      // 设置页面标题
      if (options.category_name) {
        wx.setNavigationBarTitle({
          title: options.category_name
        });
      }
    }
    
    // 加载分类列表
    this.loadCategories().then(() => {
      // 如果URL中有分类ID，设置当前选中的分类索引
      if (this.data.categoryId) {
        const index = this.data.categories.findIndex(
          item => item.id.toString() === this.data.categoryId.toString()
        );
        if (index !== -1) {
          this.setData({ currentCategoryIndex: index });
        }
      }
    });
    
    this.loadOutfits();
    
    // 记录页面第一次加载时间
    this.lastLoadTime = Date.now();
  },
  
  onShow: function() {
    const currentTime = Date.now();
    
    // 检查是否从日历页面跳转而来（通过全局变量判断）
    if (app.globalData.fromCalendarPage) {
      console.log('检测到从日历页面跳转而来');
      this.setData({
        fromCalendar: true
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '选择穿搭'
      });
    }
    
    // 每次显示页面时，都强制刷新数据以获取最新点赞数量
    console.log('刷新穿搭列表以获取最新点赞数');
    
    // 清除本地存储的穿搭数据，确保从服务器获取最新数据
    try {
      wx.removeStorageSync('outfits');
      console.log('已清除本地穿搭数据缓存');
    } catch (e) {
      console.error('清除本地穿搭数据缓存失败:', e);
    }
    
    this.setData({
      currentPage: 1,
      hasMoreData: true
    });
    
    this.loadOutfits();
    app.globalData.needRefreshOutfits = false;
    
    // 重置日历跳转标记，避免重复处理
    if (app.globalData.fromCalendarPage) {
      app.globalData.fromCalendarPage = false;
    }
    
    this.lastLoadTime = currentTime;
    
    // 如果需要刷新分类列表
    if (app.globalData.needRefreshOutfitCategories) {
      this.loadCategories();
      app.globalData.needRefreshOutfitCategories = false;
    }
  },
  
  // 加载分类列表
  loadCategories: function() {
    // 如果已经在加载中，直接返回
    if (this.data.loadingCategories) {
      return Promise.resolve();
    }
    
    this.setData({ loadingCategories: true });
    
    return new Promise((resolve) => {
      // 检查登录状态
      if (!app.globalData.token) {
        this.setData({ 
          categories: [],
          loadingCategories: false
        });
        resolve();
        return;
      }
      
      wx.request({
        url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        data: {
          page: 1,
          per_page: 100 // 获取足够多的分类
        },
        success: (res) => {
          console.log('获取穿搭分类列表:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const categories = res.data.data || [];
            this.setData({ categories });
          }
        },
        fail: (err) => {
          console.error('获取分类列表失败:', err);
        },
        complete: () => {
          this.setData({ loadingCategories: false });
          resolve();
        }
      });
    });
  },
  
  // 切换分类弹出框显示/隐藏
  toggleCategoryPopup: function() {
    this.setData({
      showCategoryPopup: !this.data.showCategoryPopup
    });
  },
  
  // 关闭分类弹出框
  closeCategoryPopup: function() {
    this.setData({
      showCategoryPopup: false
    });
  },
  
  // 跳转到添加分类页面
  goToAddCategory: function() {
    wx.navigateTo({
      url: '/pages/outfit_categories/add/add'
    });
  },
  
  // 跳转到分类管理页面
  goToCategoryManage: function() {
    wx.navigateTo({
      url: '/pages/outfit_categories/index/index'
    });
    // 关闭弹出框
    this.setData({
      showCategoryPopup: false
    });
  },
  
  // 切换分类，修改为关闭弹出框
  switchCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    
    // 如果点击的是已选中的分类，不做任何操作
    if (index === this.data.currentCategoryIndex) {
      this.closeCategoryPopup(); // 关闭弹出框
      return;
    }
    
    // 设置新的分类ID和索引
    let categoryId = null;
    let categoryName = '我的穿搭';
    
    if (index > -1) {
      // 选择了具体分类
      const category = this.data.categories[index];
      categoryId = category.id;
      categoryName = category.name;
    }
    
    this.setData({
      currentCategoryIndex: index,
      categoryId: categoryId,
      categoryName: categoryName,
      loading: true, // 重新进入加载状态
      currentPage: 1, // 重置页码
      hasMoreData: true, // 重置加载更多状态
      showCategoryPopup: false // 关闭弹出框
    });
    
    // 更新页面标题
    wx.setNavigationBarTitle({
      title: categoryName
    });
    
    // 重新加载穿搭数据
    this.loadOutfits();
  },
  
  // 加载穿搭列表
  loadOutfits: function(isLoadMore = false) {
    console.log('开始加载穿搭列表', isLoadMore ? '加载更多' : '刷新');
    
    // 如果是刷新，重置页码
    if (!isLoadMore) {
      this.setData({ 
        loading: true,
        currentPage: 1,
        hasMoreData: true
      });
    } else {
      // 加载更多，设置loadingMore状态
      this.setData({ loadingMore: true });
    }
    
    const page = this.data.currentPage;

    // 构建API请求参数
    let apiUrl = `${app.globalData.apiBaseUrl}/get_outfits.php`;
    let params = {
      page: page,
      per_page: 20
    };

    // 添加分类过滤参数
    if (this.data.categoryId) {
      params.category_id = this.data.categoryId;
    }

    // 新增：添加圈子数据参数
    if (this.data.dataSource !== 'personal') {
      params.include_circle_data = 'true';
      params.data_source = this.data.dataSource;
    }

    // 构建查询字符串
    const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
    const fullUrl = `${apiUrl}?${queryString}`;

    console.log('请求穿搭数据:', fullUrl);

    // 直接调用后端API
    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('穿搭API响应:', res.data);

        if (res.data.success) {
          const serverOutfits = res.data.data || [];
          const hasMore = res.data.pagination ? res.data.pagination.current_page < res.data.pagination.total_pages : false;
          console.log('从服务器获取到穿搭数据', serverOutfits ? serverOutfits.length : 0, '条，是否有更多:', hasMore);

          // 如果成功从服务器获取数据，更新界面
          if (serverOutfits && serverOutfits.length >= 0) {
            // 按创建时间降序排序
            serverOutfits.sort((a, b) => {
              return this.safeParseDateForSort(b.created_at) - this.safeParseDateForSort(a.created_at);
            });

            // 如果有分类ID，筛选该分类下的穿搭
            let filteredOutfits = serverOutfits;
            if (this.data.categoryId) {
              // 输出调试信息
              console.log('开始筛选分类，当前categoryId:', this.data.categoryId, '类型:', typeof this.data.categoryId);

              filteredOutfits = serverOutfits.filter(outfit => {
                // 确保category_id存在且为字符串类型进行比较
                const outfitCategoryId = outfit.category_id ? String(outfit.category_id) : '';
                const currentCategoryId = String(this.data.categoryId);

                // 输出详细的调试信息
                console.log('比较:',
                  '穿搭ID:', outfit.id,
                  '穿搭名称:', outfit.name,
                  '分类ID:', outfitCategoryId,
                  '类型:', typeof outfit.category_id,
                  '目标分类ID:', currentCategoryId,
                  '匹配结果:', outfitCategoryId === currentCategoryId
                );

                return outfitCategoryId === currentCategoryId;
              });

              console.log(`已筛选分类ID为${this.data.categoryId}的穿搭:`, filteredOutfits.length, '条');
            }

            // 预处理每个穿搭的items，确保位置信息正确
            this.processOutfitsData(filteredOutfits);

            // 如果是加载更多，则追加数据，否则替换数据
            let newOutfits = filteredOutfits;
            if (isLoadMore && this.data.outfits.length > 0) {
              // 为防止重复，根据id过滤出新数据
              const existingIds = this.data.outfits.map(item => item.id);
              const uniqueNewOutfits = filteredOutfits.filter(item => !existingIds.includes(item.id));
              newOutfits = [...this.data.outfits, ...uniqueNewOutfits];
              console.log('加载更多，合并后总数据:', newOutfits.length, '条');
            }

            // 保存原始穿搭列表用于搜索
            this.setData({
              outfits: newOutfits,
              originalOutfits: newOutfits,
              loading: false,
              isEmpty: newOutfits.length === 0,
              loadingMore: false,
              hasMoreData: hasMore  // 更新是否有更多数据标志
            });

            // 如果有搜索关键词，应用搜索过滤
            if (this.data.outfitSearchKeyword && this.data.isSearchMode) {
              this.applySearchFilter();
            }

            // 如果当前页有数据，页码+1为下次做准备
            if (filteredOutfits.length > 0) {
              this.setData({
                currentPage: this.data.currentPage + 1
              });
            }
          } else {
            // 如果从服务器获取数据失败，显示空状态
            console.log('无法获取穿搭数据，显示空状态');
            this.setData({
              loading: false,
              loadingMore: false,
              isEmpty: this.data.outfits.length === 0,
              hasMoreData: false
            });
          }
        } else {
          console.error('获取穿搭数据失败:', res.data);
          this.setData({
            loading: false,
            loadingMore: false,
            isEmpty: this.data.outfits.length === 0
          });
        }
      },
      fail: (err) => {
        console.error('穿搭API请求失败:', err);
        this.setData({
          loading: false,
          loadingMore: false,
          isEmpty: this.data.outfits.length === 0
        });
      }
    });

    // 移除本地数据处理逻辑，因为我们现在直接使用API
    /*if (localOutfits && localOutfits.length > 0 && !isLoadMore) {
      console.log('使用本地穿搭数据', localOutfits.length, '条');
      
      // 按创建时间降序排序
      localOutfits.sort((a, b) => {
        return this.safeParseDateForSort(b.created_at) - this.safeParseDateForSort(a.created_at);
      });
      
      // 如果有分类ID，筛选该分类下的穿搭
      let filteredLocalOutfits = localOutfits;
      if (this.data.categoryId) {
        // 输出调试信息
        console.log('开始筛选本地穿搭分类，当前categoryId:', this.data.categoryId, '类型:', typeof this.data.categoryId);
        
        filteredLocalOutfits = localOutfits.filter(outfit => {
          // 确保category_id存在且为字符串类型进行比较
          const outfitCategoryId = outfit.category_id ? String(outfit.category_id) : '';
          const currentCategoryId = String(this.data.categoryId);
          
          // 输出详细的调试信息
          console.log('本地比较:', 
            '穿搭ID:', outfit.id,
            '穿搭名称:', outfit.name,
            '分类ID:', outfitCategoryId, 
            '类型:', typeof outfit.category_id,
            '目标分类ID:', currentCategoryId,
            '匹配结果:', outfitCategoryId === currentCategoryId
          );
          
          return outfitCategoryId === currentCategoryId;
        });
        
        console.log(`已筛选本地分类ID为${this.data.categoryId}的穿搭:`, filteredLocalOutfits.length, '条');
      }
      
      // 预处理每个穿搭的items，确保位置信息正确
      this.processOutfitsData(filteredLocalOutfits);
      
      this.setData({
        outfits: filteredLocalOutfits,
        loading: true, // 总是显示加载状态，等待服务器响应
        isEmpty: filteredLocalOutfits.length === 0
      });
      
      // 在任何情况下，设置一个最大加载时间
      setTimeout(() => {
        if (this.data.loading) {
          console.log('防止永久加载状态，超时自动使用本地数据');
          this.setData({ 
            loading: false 
          });
        }
      }, 3000); // 最多加载3秒钟
    } else {
      console.log('本地无穿搭数据或正在加载更多，等待服务器响应');

      // 如果没有本地数据，等待服务器响应，同时设置超时
      setTimeout(() => {
        // 如果还在加载中（服务器未响应），显示空状态
        if (this.data.loading || this.data.loadingMore) {
          console.log('服务器响应超时，不再显示加载状态');
          this.setData({
            loading: false,
            loadingMore: false,
            isEmpty: this.data.outfits.length === 0
          });
        }
      }, 5000); // 增加超时时间，给服务器更多响应时间
    }*/
  },

  // 新增：数据源切换相关方法
  toggleDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: !this.data.showDataSourcePopup
    });
  },

  closeDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: false
    });
  },

  switchDataSource: function(e) {
    const dataSource = e.currentTarget.dataset.source;
    console.log("切换数据源到:", dataSource);

    this.setData({
      dataSource: dataSource,
      showDataSourcePopup: false,
      loading: true,
      currentPage: 1,
      hasMoreData: true
    });

    // 重新加载数据
    this.loadOutfits();
  },

  // 处理穿搭数据，确保衣物位置信息正确
  processOutfitsData: function(outfits) {
    if (!outfits || !outfits.length) return;
    
    console.log('处理穿搭数据:', outfits.map(o => ({id: o.id, name: o.name, likes: o.likes_count, is_public: o.is_public})));
    
    outfits.forEach(outfit => {
      // 确保likes_count字段是数字
      if (outfit.likes_count !== undefined) {
        outfit.likes_count = parseInt(outfit.likes_count) || 0;
      } else {
        outfit.likes_count = 0;
      }
      
      // 确保is_public字段是布尔值
      if (outfit.is_public === undefined) {
        outfit.is_public = false;
      } else {
        // 确保转换为布尔值，避免字符串比较问题
        outfit.is_public = outfit.is_public === true || outfit.is_public === 'true' || outfit.is_public === 1 || outfit.is_public === '1';
      }
      if (outfit.items && outfit.items.length > 0) {
        // 计算所有衣物的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        // 首先确保每个衣物项有正确的位置信息，并计算边界
        outfit.items.forEach(item => {
          // 防止位置信息缺失导致显示异常
          if (!item.position) {
            item.position = { x: 50, y: 50 };
          }
          if (!item.size) {
            item.size = { width: 100, height: 120 };
          }
          if (item.rotation === undefined) {
            item.rotation = 0;
          }
          if (item.z_index === undefined) {
            item.z_index = 1;
          }
          
          // 计算衣物的边界
          const left = item.position.x;
          const top = item.position.y;
          const right = left + item.size.width;
          const bottom = top + item.size.height;
          
          // 更新整体边界
          minX = Math.min(minX, left);
          minY = Math.min(minY, top);
          maxX = Math.max(maxX, right);
          maxY = Math.max(maxY, bottom);
        });
        
        // 计算边界框的宽度和高度
        const boundingWidth = maxX - minX;
        const boundingHeight = maxY - minY;
        
        // 计算边界框的中心点
        const centerX = minX + boundingWidth / 2;
        const centerY = minY + boundingHeight / 2;
        
        // 为每个衣物计算预览列表中的居中坐标
        // 假设预览容器的中心点是 (90, 90)，因为outfit-preview大约是180px高宽
        const previewCenterX = 90;
        const previewCenterY = 90;
        
        // 计算最合适的缩放比例，确保整个穿搭都在预览框内
        const maxDimension = Math.max(boundingWidth, boundingHeight);
        const maxAllowedSize = 160; // 预留一些边距
        const scale = maxDimension > 0 ? Math.min(maxAllowedSize / maxDimension, 0.5) : 0.5;
        
        // 为每个衣物添加预览坐标，用于在列表页显示
        outfit.items.forEach(item => {
          item.previewPosition = {
            x: previewCenterX + (item.position.x - centerX) * scale,
            y: previewCenterY + (item.position.y - centerY) * scale,
            scale: scale
          };
        });
      }
      
      // 格式化日期显示
      if (outfit.created_at) {
        try {
          // 处理iOS不兼容的日期格式进行显示
          let dateStr = outfit.created_at;
          let dateObj;
          
          // 尝试转换为iOS兼容格式
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
            dateStr = dateStr.replace(' ', 'T');
          }
          
          dateObj = new Date(dateStr);
          
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
            const day = dateObj.getDate().toString().padStart(2, '0');
            outfit.formatted_date = `${year}/${month}/${day}`;
          } else {
            outfit.formatted_date = outfit.created_at.split(' ')[0].replace(/-/g, '/');
          }
        } catch (err) {
          console.error('日期格式化错误:', err, outfit.created_at);
          outfit.formatted_date = '日期错误';
        }
      }
    });
    
    return outfits;
  },
  
  // 前往添加穿搭页面
  navigateToAdd: function() {
    wx.navigateTo({
      url: "/pages/outfits/add/add"
    });
  },
  
  // 前往穿搭广场页面
  navigateToSquare: function() {
    wx.navigateTo({
      url: "/pages/outfit_square/index/index"
    });
  },
  
  // 查看穿搭详情
  viewOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/outfits/detail/detail?id=${outfitId}`
    });
  },
  
  // 处理长按穿搭卡片
  onLongPress: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name || '未命名穿搭';
    
    wx.showActionSheet({
      itemList: ['删除穿搭', '编辑穿搭'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 删除穿搭
          this.deleteOutfit(id, name);
        } else if (res.tapIndex === 1) {
          // 编辑穿搭
          this.editOutfit(id);
        }
      },
      fail: () => {}
    });
  },
  
  // 删除穿搭
  deleteOutfit: function(outfitId, outfitName) {
    // 显示确认对话框
    wx.showModal({
      title: "确认删除",
      content: `确定要删除 "${outfitName}" 穿搭吗？`,
      confirmColor: "#FF0000",
      success: (result) => {
        if (result.confirm) {
          // 显示删除中状态
          wx.showLoading({
            title: '删除中...',
          });
          
          // 调用删除接口
          wx.request({
            url: `${app.globalData.apiBaseUrl}/delete_outfit.php`,
            method: 'POST',
            header: {
              'Authorization': app.globalData.token,
              'Content-Type': 'application/json'
            },
            data: {
              id: outfitId
            },
            success: (res) => {
              wx.hideLoading();
              
              console.log('删除穿搭结果:', res.data);
              
              if (res.statusCode === 200 && res.data.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 从列表中移除已删除的穿搭
                const updatedOutfits = this.data.outfits.filter(item => item.id !== outfitId);
                
                // 更新数据
                this.setData({
                  outfits: updatedOutfits,
                  isEmpty: updatedOutfits.length === 0
                });
                
                // 删除本地缓存
                app.removeOutfit(outfitId);
              } else {
                wx.showToast({
                  title: res.data.msg || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('删除穿搭请求失败:', err);
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    // 重置页码
    this.setData({
      currentPage: 1,
      hasMoreData: true
    });
    
    // 同时刷新穿搭列表和分类列表
    this.loadCategories().then(() => {
      this.loadOutfits();
      wx.stopPullDownRefresh();
    });
  },
  
  // 分享功能
  onShareAppMessage: function() {
    return {
      title: "我的穿搭集合，创建属于你的个性时尚！",
      path: "/pages/outfits/index/index"
    };
  },

  // 安全的日期解析方法
  safeParseDateForSort: function(dateStr) {
    if (!dateStr) return 0;
    
    try {
      // 检查是否是 "yyyy-MM-dd HH:mm:ss" 格式（iOS不兼容的格式）
      if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
        // 转换为iOS兼容的格式 "yyyy-MM-ddTHH:mm:ss"
        dateStr = dateStr.replace(' ', 'T');
      }
      
      const date = new Date(dateStr);
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateStr);
        return 0;
      }
      
      return date.getTime();
    } catch (err) {
      console.error('日期解析错误:', err, dateStr);
      return 0;
    }
  },
  
  // 触底加载更多
  onReachBottom: function() {
    console.log('滚动到底部，是否有更多数据:', this.data.hasMoreData);
    
    // 如果没有更多数据或者正在加载，不处理
    if (!this.data.hasMoreData || this.data.loading || this.data.loadingMore) {
      console.log('无需加载更多');
      return;
    }
    
    console.log('加载更多穿搭数据，当前页码:', this.data.currentPage);
    this.loadOutfits(true); // 传入true表示加载更多
  },
  
  // scroll-view滚动到底部加载更多
  loadMore: function(e) {
    console.log('滚动到底部事件触发', e);
    console.log('当前数据状态:', '是否有更多:', this.data.hasMoreData, '正在加载:', this.data.loading, '加载更多中:', this.data.loadingMore);
    
    // 如果没有更多数据或者正在加载，不处理
    if (!this.data.hasMoreData || this.data.loading || this.data.loadingMore) {
      console.log('无需加载更多');
      return;
    }
    
    console.log('加载更多穿搭数据，当前页码:', this.data.currentPage);
    wx.showToast({
      title: '加载更多...',
      icon: 'loading',
      duration: 500
    });
    this.loadOutfits(true); // 传入true表示加载更多
  },

  // 跳转到智能穿搭页面
  goToSmartOutfit: function() {
    wx.navigateTo({
      url: '/pages/smart_outfit/smart_outfit'
    });
  },

  // 编辑穿搭
  editOutfit: function(outfitId) {
    // 实现编辑穿搭的逻辑
  },

  // 点击穿搭项
  onOutfitItemTap: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    console.log('点击穿搭:', outfitId);
    
    // 如果是从日历页面跳转而来，则选择该穿搭并返回
    if (this.data.fromCalendar) {
      // 查找选中的穿搭
      const selectedOutfit = this.data.outfits.find(item => item.id === outfitId);
      
      if (selectedOutfit) {
        // 将选中的穿搭保存到全局变量
        app.globalData.selectedOutfitForCalendar = selectedOutfit;
        
        // 返回日历页面
        wx.navigateBack();
      }
    } else {
      // 正常查看穿搭详情
      wx.navigateTo({
        url: `/pages/outfits/detail/detail?id=${outfitId}`
      });
    }
  },

  // 搜索输入事件
  onOutfitSearchInput: function(e) {
    const keyword = e.detail.value.trim();
    this.setData({
      outfitSearchKeyword: keyword
    });
    
    // 延迟搜索，避免频繁更新
    if (this._searchTimer) {
      clearTimeout(this._searchTimer);
    }
    
    this._searchTimer = setTimeout(() => {
      this.applySearchFilter();
    }, 300);
  },
  
  // 清除搜索
  clearOutfitSearch: function() {
    this.setData({
      outfitSearchKeyword: '',
      isSearchMode: false,
      outfits: this.data.originalOutfits,
    });
  },
  
  // 执行搜索
  searchOutfits: function() {
    this.applySearchFilter();
  },
  
  // 应用搜索过滤
  applySearchFilter: function() {
    const keyword = this.data.outfitSearchKeyword;
    
    // 如果关键词为空，恢复原始列表
    if (!keyword) {
      this.setData({
        outfits: this.data.originalOutfits,
        isSearchMode: false
      });
      return;
    }
    
    // 设置为搜索模式
    this.setData({
      isSearchMode: true
    });
    
    // 从原始列表中筛选匹配的穿搭
    const filteredOutfits = this.data.originalOutfits.filter(outfit => {
      // 匹配穿搭名称(不区分大小写)
      return outfit.name && outfit.name.toLowerCase().includes(keyword.toLowerCase());
    });
    
    console.log(`搜索关键词: "${keyword}", 找到 ${filteredOutfits.length} 条匹配穿搭`);
    
    // 更新UI
    this.setData({
      outfits: filteredOutfits,
      isEmpty: filteredOutfits.length === 0
    });
  },
}); 