<!-- 共同管理衣橱主页 -->
<!-- 模块1：圈子基础管理模块 -->

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 未加入圈子状态 -->
  <view class="no-circle-container" wx:if="{{!loading && !hasCircle}}">
    <view class="welcome-section">
      <image class="welcome-icon" src="/images/outfit.png" mode="aspectFit"></image>
      <text class="welcome-title">共同管理衣橱穿搭</text>
      <text class="welcome-desc">与朋友一起管理衣橱，分享穿搭灵感</text>
    </view>

    <view class="action-section">
      <view class="action-card" bindtap="showCreateCircle">
        <view class="action-icon">👑</view>
        <view class="action-content">
          <text class="action-title">我要当创建者</text>
          <text class="action-desc">创建圈子，邀请朋友一起管理衣橱</text>
        </view>
        <view class="action-arrow">→</view>
      </view>

      <view class="action-card" bindtap="showJoinCircle">
        <view class="action-icon">🤝</view>
        <view class="action-content">
          <text class="action-title">我要加入其他圈子</text>
          <text class="action-desc">通过邀请码加入朋友的圈子</text>
        </view>
        <view class="action-arrow">→</view>
      </view>
    </view>

    <view class="info-section">
      <text class="info-title">加入圈子后将与圈子内人员共享：</text>
      <view class="info-list">
        <text class="info-item">• 衣橱分类和衣物信息</text>
        <text class="info-item">• 穿搭分类和穿搭信息</text>
        <text class="info-item">• 衣物标签和搭配记录</text>
      </view>
    </view>
  </view>

  <!-- 已加入圈子状态 - 创建者视图 -->
  <view class="circle-container" wx:if="{{!loading && hasCircle && userRole === 'creator'}}">
    <view class="circle-header">
      <text class="circle-name">{{circle.name}}</text>
      <view class="circle-code-section">
        <text class="code-label">邀请码：</text>
        <text class="code-value">{{circle.invitation_code}}</text>
        <view class="copy-btn" bindtap="copyInvitationCode">
          <image class="copy-icon" src="/images/share.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{circle.member_count}}</text>
        <text class="stat-label">成员数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.wardrobe_count}}</text>
        <text class="stat-label">衣橱数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.clothes_count}}</text>
        <text class="stat-label">衣物总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.outfit_count}}</text>
        <text class="stat-label">穿搭总数</text>
      </view>
    </view>

    <view class="members-section">
      <text class="section-title">圈子成员</text>
      <view class="members-list">
        <view class="member-item" wx:for="{{circle.members}}" wx:key="user_id">
          <view class="member-content" data-member="{{item}}" bindtap="viewMemberDetail">
            <image class="member-avatar" src="{{item.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="member-info">
              <view class="member-basic">
                <text class="member-name">{{item.nickname}}</text>
                <text class="member-role">{{item.role === 'creator' ? '创建者' : '成员'}}</text>
              </view>
              <view class="member-stats">
                <text class="stat-item">衣物 {{item.stats.clothes_count}}</text>
                <text class="stat-item">穿搭 {{item.stats.outfit_count}}</text>
                <text class="stat-item">总贡献 {{item.stats.total_contributions}}</text>
              </view>
              <text class="member-time">加入时间：{{item.joined_at}}</text>
            </view>
          </view>
          <view class="member-actions">
            <view class="contribution-badge" wx:if="{{item.stats.total_contributions > 0}}">
              <text class="badge-text">{{item.stats.total_contributions}}</text>
            </view>
            <text class="remove-btn" wx:if="{{item.role !== 'creator' && !item.is_current_user && userRole === 'creator'}}" data-member="{{item}}" bindtap="showKickMember" catchtap="showKickMember">移除</text>
          </view>
        </view>
      </view>
    </view>

    <view class="bottom-actions">
      <button class="shared-data-btn" bindtap="goToSharedData">数据共享</button>
      <button class="invite-btn" bindtap="shareInvitation">邀请好友</button>
    </view>
  </view>

  <!-- 已加入圈子状态 - 成员视图 -->
  <view class="circle-container" wx:if="{{!loading && hasCircle && userRole === 'member'}}">
    <view class="circle-header">
      <text class="circle-name">{{circle.name}}</text>
      <text class="circle-desc">{{circle.description}}</text>
    </view>

    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{circle.member_count}}</text>
        <text class="stat-label">成员数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.wardrobe_count}}</text>
        <text class="stat-label">衣橱数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.clothes_count}}</text>
        <text class="stat-label">衣物总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{circle.stats.outfit_count}}</text>
        <text class="stat-label">穿搭总数</text>
      </view>
    </view>

    <view class="join-info">
      <text class="join-time">加入时间：{{circle.user_joined_at}}</text>
    </view>

    <view class="bottom-actions">
      <button class="shared-data-btn" bindtap="goToSharedData">数据共享</button>
      <button class="leave-btn" bindtap="showLeaveCircle">退出圈子</button>
    </view>
  </view>


</view>

<!-- 创建圈子弹框 -->
<view class="modal-mask" wx:if="{{showCreateModal}}" bindtap="hideCreateModal">
  <view class="modal-content" catchtap="stopCreateModalClose">
    <view class="modal-header">
      <text class="modal-title">创建圈子</text>
      <view class="modal-close" bindtap="hideCreateModal">×</view>
    </view>
    
    <view class="modal-body">
      <text class="modal-desc">加入者将与你共享衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息</text>
      
      <view class="form-group">
        <text class="form-label">圈子名称</text>
        <input class="form-input"
               placeholder="请输入圈子名称"
               placeholder-style="color: #999; font-size: 28rpx; line-height: 40rpx; text-align: left; vertical-align: middle;"
               value="{{createForm.name}}"
               bindinput="onCreateNameInput"
               maxlength="50"
               adjust-position="{{false}}"
               hold-keyboard="{{true}}" />
      </view>
      
      <view class="form-group">
        <text class="form-label">备注内容（可选）</text>
        <textarea class="form-textarea"
                  placeholder="请输入备注内容"
                  placeholder-style="color: #999; font-size: 28rpx;"
                  value="{{createForm.description}}"
                  bindinput="onCreateDescInput"
                  maxlength="500"></textarea>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideCreateModal">取消</button>
      <button class="confirm-btn" bindtap="createCircle" disabled="{{submitting}}">
        {{submitting ? '创建中...' : '确定'}}
      </button>
    </view>
  </view>
</view>

<!-- 加入圈子弹框 -->
<view class="modal-mask" wx:if="{{showJoinModal}}" bindtap="hideJoinModal">
  <view class="modal-content" catchtap="stopJoinModalClose">
    <view class="modal-header">
      <text class="modal-title">加入圈子</text>
      <view class="modal-close" bindtap="hideJoinModal">×</view>
    </view>
    
    <view class="modal-body">
      <text class="modal-desc">加入圈子后将与圈子内人员共享衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息</text>
      
      <view class="form-group">
        <text class="form-label">邀请码</text>
        <input class="form-input"
               placeholder="请输入邀请码"
               placeholder-style="color: #999; font-size: 28rpx; line-height: 40rpx; text-align: left; vertical-align: middle;"
               value="{{joinForm.invitationCode}}"
               bindinput="onJoinCodeInput"
               maxlength="20"
               adjust-position="{{false}}"
               hold-keyboard="{{true}}" />
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideJoinModal">取消</button>
      <button class="confirm-btn" bindtap="joinCircle" disabled="{{submitting}}">
        {{submitting ? '加入中...' : '确定'}}
      </button>
    </view>
  </view>
</view>

<!-- 数据同步弹框 -->
<view class="modal-overlay" wx:if="{{showSyncModal}}" bindtap="hideSyncModal">
  <view class="sync-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">同步数据到圈子</text>
    </view>
    <view class="modal-content">
      <text class="modal-desc">点击确定进入数据共享界面进行同步操作</text>
    </view>
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="hideSyncModal">取消</button>
      <button class="confirm-btn" bindtap="startSync">确定</button>
    </view>
  </view>
</view>
