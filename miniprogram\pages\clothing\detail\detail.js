const app = getApp();
const permission = require('../../../utils/permission');
const tagParser = require('../../../utils/tagParser.js');

Page({
  data: {
    clothingId: null,
    clothingDetail: null,
    loading: true,
    descriptionObj: {}, // 解析后的描述JSON对象
    tags: [], // 标签数组
    tagColors: [],
    showImageModal: false, // 是否显示大图弹框
    isMerchantMode: false, // 判断是否从商家衣橱页面进入
    merchantId: null,
    relatedOutfits: [], // 关联的穿搭列表
    loadingRelatedOutfits: false, // 关联穿搭加载状态

    // 新增：权限相关
    canEdit: false,
    canDelete: false,
    isOwner: false,
    userRole: '',
    permissionLoading: true
  },
  
  // 分享给好友
  onShareAppMessage: function() {
    const clothing = this.data.clothingDetail;
    if (!clothing) {
      return {
        title: '来看看我的衣物吧',
        path: '/pages/index/index'
      };
    }

    return {
      title: clothing.name || '来看看我的衣物吧',
      imageUrl: clothing.image_url,
      path: `/pages/clothing/detail/detail?id=${this.data.clothingId}`
    };
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    const clothing = this.data.clothingDetail;
    if (!clothing) {
      return {
        title: '次元衣帽间-打造专属你的时尚衣橱',
        query: ''
      };
    }

    return {
      title: clothing.name || '来看看我的衣物吧',
      imageUrl: clothing.image_url,
      query: `id=${this.data.clothingId}`
    };
  },
  
  onLoad: function (options) {
    // 检查是否登录
    if (!app.globalData.token) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
    
    // 获取并设置衣物ID
    if (options.id) {
      this.setData({
        clothingId: options.id,
        isMerchantMode: !!options.merchant_id, // 判断是否从商家衣橱页面进入
        merchantId: options.merchant_id || null
      });
      
      // 加载衣物详情
      this.loadClothingDetail();
    } else {
      wx.showToast({
        title: '缺少衣物ID参数',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载衣物详情
  loadClothingDetail: function() {
    if (!this.data.clothingId) {
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });
    
    console.log("开始获取衣物详情, ID:", this.data.clothingId);
    
    // 构建请求URL，添加圈子数据支持
    let url = `${app.globalData.apiBaseUrl}/get_clothes.php?id=${this.data.clothingId}`;

    // 添加圈子数据参数，确保能获取到共享的衣物
    url += '&include_circle_data=true&data_source=all';

    console.log("请求URL:", url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        // 隐藏加载中
        wx.hideLoading();
        
        console.log("获取衣物详情响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          // 因为按ID查询应该只返回一个衣物，取第一个
          const clothingDetail = res.data.data[0];
          
          // 解析描述字段的JSON数据
          let descriptionObj = {};
          try {
            if (clothingDetail.description) {
              descriptionObj = JSON.parse(clothingDetail.description);
            }
          } catch (e) {
            console.error("解析描述JSON失败:", e);
          }
          
          // 解析标签字符串为数组
          let tags = [];
          if (clothingDetail.tags) {
            try {
              // 使用标签解析工具解析标签
              console.log("原始标签字符串:", clothingDetail.tags);
              tags = tagParser.parseTags(clothingDetail.tags);
              console.log("最终处理后的标签数组:", tags);
            } catch (e) {
              console.error("解析标签失败:", e, "原始标签:", clothingDetail.tags);
              // 解析失败时，回退到原始方法
              tags = clothingDetail.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
            }
          }
          
          // 添加分类作为标签
          const categoryMap = {
            'tops': '上衣',
            'pants': '裤子',
            'skirts': '裙子',
            'coats': '外套',
            'shoes': '鞋子',
            'bags': '包包',
            'accessories': '配饰'
          };
          
          if (clothingDetail.category && categoryMap[clothingDetail.category]) {
            tags.unshift(categoryMap[clothingDetail.category]);
          }
          
          // 为每个标签生成随机颜色
          const tagColors = this.generateTagColors(tags.length);
          
          // 解析描述字段
          if (clothingDetail.description) {
            try {
              const description = JSON.parse(clothingDetail.description);
              clothingDetail.color = description.color || '';
              clothingDetail.brand = description.brand || '';
              clothingDetail.price = description.price || '';
              clothingDetail.notes = description.notes || ''; // 添加备注字段
            } catch (e) {
              console.error('解析描述字段失败:', e);
            }
          }

          this.setData({
            clothingDetail: clothingDetail,
            descriptionObj: descriptionObj,
            tags: tags,
            tagColors: tagColors,
            loading: false
          });

          // 动态设置标签颜色
          this.setTagColors();

          // 检查权限
          this.checkPermissions();

          // 加载关联穿搭
          this.loadRelatedOutfits();
        } else {
          // 处理错误
          console.error("获取衣物详情失败:", res.data.msg || '未找到衣物数据');
          
          wx.showToast({
            title: res.data.msg || '未找到衣物数据',
            icon: 'none'
          });
          
          // 2秒后返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      },
      fail: (err) => {
        // 隐藏加载中
        wx.hideLoading();
        
        // 处理网络错误
        console.error("获取衣物详情网络错误:", err);
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 生成随机标签颜色
  generateTagColors: function(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
      // 使用HSL颜色模型生成柔和的颜色
      const hue = Math.floor(Math.random() * 360); // 随机色相
      const saturation = 70 + Math.floor(Math.random() * 20); // 中高饱和度
      const lightness = 85 + Math.floor(Math.random() * 10); // 高亮度，柔和色调
      
      colors.push(`hsla(${hue}, ${saturation}%, ${lightness}%, 1)`);
    }
    return colors;
  },
  
  // 设置标签颜色
  setTagColors: function() {
    const tags = this.data.tags;
    const colors = this.data.tagColors;
    
    setTimeout(() => {
      tags.forEach((tag, index) => {
        const randomColor = colors[index];
        const tagElements = wx.createSelectorQuery().selectAll(`.tag:nth-child(${index + 1})`);
        
        tagElements.fields({ node: true, properties: [] }).exec((res) => {
          if (res[0] && res[0].length > 0) {
            res[0].forEach(tagNode => {
              tagNode.node.style.setProperty('--random-color', randomColor);
              tagNode.node.style.setProperty('--index', index);
            });
          }
        });
      });
    }, 100);
  },
  
  // 返回上一页
  navigateBack: function() {
    console.log("返回上一页");
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，则跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  },
  
  // 显示衣物大图
  showLargeImage: function() {
    console.log("显示衣物大图");
    if (this.data.clothingDetail && this.data.clothingDetail.image_url) {
      this.setData({
        showImageModal: true
      });
    }
  },
  
  // 隐藏衣物大图
  hideLargeImage: function() {
    console.log("隐藏衣物大图");
    this.setData({
      showImageModal: false
    });
  },
  
  // 阻止事件冒泡
  preventDefault: function(e) {
    // 此函数仅用于阻止事件冒泡
    return;
  },
  
  // 保存图片到手机
  saveImageToPhone: function() {
    const that = this;
    if (!this.data.clothingDetail || !this.data.clothingDetail.image_url) {
      wx.showToast({
        title: '无图片可保存',
        icon: 'none'
      });
      return;
    }
    
    console.log("开始保存图片:", this.data.clothingDetail.image_url);
    
    // 获取用户授权
    wx.getSetting({
      success(res) {
        // 如果没有写入相册的权限
        if (!res.authSetting['scope.writePhotosAlbum']) {
          // 请求授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success() {
              // 用户同意授权，保存图片
              that.downloadAndSaveImage();
            },
            fail() {
              // 用户拒绝授权，引导打开设置页
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                confirmText: '去授权',
                success(res) {
                  if (res.confirm) {
                    wx.openSetting({
                      success(res) {
                        if (res.authSetting['scope.writePhotosAlbum']) {
                          // 用户在设置页同意了授权
                          that.downloadAndSaveImage();
                        }
                      }
                    });
                  }
                }
              });
            }
          });
        } else {
          // 已有授权，直接保存
          that.downloadAndSaveImage();
        }
      },
      fail(err) {
        console.error("获取授权设置失败:", err);
        wx.showToast({
          title: '保存失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 下载并保存图片
  downloadAndSaveImage: function() {
    const that = this;
    const imageUrl = this.data.clothingDetail.image_url;
    
    wx.showLoading({
      title: '保存中...',
    });
    
    // 下载图片
    wx.downloadFile({
      url: imageUrl,
      success(res) {
        if (res.statusCode === 200) {
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success() {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail(err) {
              wx.hideLoading();
              console.error("保存图片失败:", err);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '图片下载失败',
            icon: 'none'
          });
        }
      },
      fail(err) {
        wx.hideLoading();
        console.error("下载图片失败:", err);
        wx.showToast({
          title: '图片下载失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 检查权限
  checkPermissions: function() {
    const clothingId = this.data.clothingId;
    if (!clothingId) return;

    // 检查编辑权限
    permission.checkCirclePermission('clothes', clothingId, 'edit')
      .then(editPermission => {
        // 检查删除权限
        return permission.checkCirclePermission('clothes', clothingId, 'delete')
          .then(deletePermission => {
            this.setData({
              canEdit: editPermission.allowed,
              canDelete: deletePermission.allowed,
              isOwner: editPermission.is_owner,
              userRole: editPermission.user_role,
              permissionLoading: false
            });

            console.log('权限检查结果:', {
              canEdit: editPermission.allowed,
              canDelete: deletePermission.allowed,
              isOwner: editPermission.is_owner,
              userRole: editPermission.user_role
            });
          });
      })
      .catch(error => {
        console.error('权限检查失败:', error);
        this.setData({
          canEdit: false,
          canDelete: false,
          permissionLoading: false
        });
      });
  },

  // 编辑衣物
  editClothing: function() {
    const id = this.data.clothingId;
    console.log("跳转到编辑页面，衣物ID:", id);

    // 使用权限工具检查并执行
    permission.checkPermissionAndExecute('clothes', id, 'edit', () => {
      wx.navigateTo({
        url: `/pages/clothing/edit/edit?id=${id}`,
        events: {
          // 监听编辑完成事件，刷新详情页数据
          editComplete: (data) => {
            if (data && data.success) {
              console.log("编辑完成，刷新详情页");
              // 重新加载衣物详情
              this.loadClothingDetail();
            }
          }
        },
        success: () => {
          console.log("成功跳转到编辑页面");
        },
        fail: (err) => {
          console.error("跳转到编辑页面失败:", err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    });
  },
  
  // 加载关联穿搭
  loadRelatedOutfits: function() {
    if (!this.data.clothingId) {
      return;
    }
    
    this.setData({ loadingRelatedOutfits: true });
    
    // 使用已有的检查衣物所在穿搭的方法获取关联穿搭
    this.checkClothingInOutfits((isUsed, outfitUsages) => {
      if (isUsed && outfitUsages && outfitUsages.length > 0) {
        console.log('关联穿搭数据:', outfitUsages);
        
        // 获取穿搭详情，例如预览图等更多信息
        this.getOutfitsDetails(outfitUsages);
      } else {
        this.setData({
          relatedOutfits: [],
          loadingRelatedOutfits: false
        });
      }
    }, true); // 传入true表示这是为了展示而非删除检查
  },
  
  // 获取穿搭详情信息
  getOutfitsDetails: function(outfitBasicList) {
    if (!outfitBasicList || outfitBasicList.length === 0) {
      this.setData({ loadingRelatedOutfits: false });
      return;
    }
    
    // 使用完整的穿搭数据，包含缩略图和items
    const relatedOutfits = outfitBasicList.map(outfit => ({
      id: outfit.id,
      name: outfit.name,
      thumbnail: outfit.thumbnail || '',
      items: this.processOutfitItems(outfit.items || [])
    }));
    
    this.setData({
      relatedOutfits: relatedOutfits,
      loadingRelatedOutfits: false
    });
  },
  
  // 处理穿搭中的衣物项，添加预览位置
  processOutfitItems: function(items) {
    if (!items || items.length === 0) return [];
    
    // 计算所有衣物的边界框
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    // 首先确保每个衣物项有正确的位置信息，并计算边界
    items.forEach(item => {
      // 防止位置信息缺失导致显示异常
      if (!item.position) {
        item.position = { x: 50, y: 50 };
      }
      if (!item.size) {
        item.size = { width: 100, height: 120 };
      }
      if (item.rotation === undefined) {
        item.rotation = 0;
      }
      if (item.z_index === undefined) {
        item.z_index = 1;
      }
      
      // 计算衣物的边界
      const left = item.position.x;
      const top = item.position.y;
      const right = left + item.size.width;
      const bottom = top + item.size.height;
      
      // 更新整体边界
      minX = Math.min(minX, left);
      minY = Math.min(minY, top);
      maxX = Math.max(maxX, right);
      maxY = Math.max(maxY, bottom);
    });
    
    // 计算边界框的宽度和高度
    const boundingWidth = maxX - minX;
    const boundingHeight = maxY - minY;
    
    // 计算边界框的中心点
    const centerX = minX + boundingWidth / 2;
    const centerY = minY + boundingHeight / 2;
    
    // 为每个衣物计算预览列表中的居中坐标
    // 假设预览容器的中心点是 (40, 40)，因为预览容器大约是80px高宽
    const previewCenterX = 40;
    const previewCenterY = 40;
    
    // 计算最合适的缩放比例，确保整个穿搭都在预览框内
    const maxDimension = Math.max(boundingWidth, boundingHeight);
    const maxAllowedSize = 70; // 预留一些边距
    const scale = maxDimension > 0 ? Math.min(maxAllowedSize / maxDimension, 0.3) : 0.3;
    
    // 为每个衣物添加预览坐标，用于在列表页显示
    items.forEach(item => {
      item.previewPosition = {
        x: previewCenterX + (item.position.x - centerX) * scale,
        y: previewCenterY + (item.position.y - centerY) * scale,
        scale: scale
      };
    });
    
    return items;
  },
  
  // 检查衣物是否在穿搭中使用
  checkClothingInOutfits: function(callback, isForDisplay = false) {
    const clothingId = this.data.clothingId;
    if (!clothingId) {
      callback(false);
      return;
    }
    
    // 如果是为了展示关联穿搭，不显示loading
    if (!isForDisplay) {
      wx.showLoading({
        title: '检查中...',
      });
    }
    
    // 调用获取穿搭列表的接口
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfits.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (!isForDisplay) {
          wx.hideLoading();
        }
        
        if (res.statusCode === 200 && res.data.success) {
          const outfits = res.data.data || [];
          
          // 检查衣物是否在任何穿搭中使用
          const outfitUsages = [];
          
          outfits.forEach(outfit => {
            if (outfit.items && outfit.items.length > 0) {
              // 查看每个穿搭的items中是否包含该衣物
              const usedInOutfit = outfit.items.some(item => 
                item.clothing_id && item.clothing_id.toString() === clothingId.toString()
              );
              
              if (usedInOutfit) {
                // 收集更多穿搭信息用于展示
                outfitUsages.push({
                  id: outfit.id,
                  name: outfit.name || '未命名穿搭',
                  thumbnail: outfit.thumbnail || '',
                  items: outfit.items || [],
                  category_name: outfit.category_name || '',
                  created_at: outfit.created_at || ''
                });
              }
            }
          });
          
          if (outfitUsages.length > 0) {
            // 衣物在穿搭中使用，返回使用情况
            callback(true, outfitUsages);
          } else {
            // 衣物未在穿搭中使用
            callback(false);
          }
        } else {
          console.error("获取穿搭列表失败:", res.data);
          // 如果无法获取穿搭列表，为安全起见，默认允许删除
          callback(false);
        }
      },
      fail: (err) => {
        if (!isForDisplay) {
          wx.hideLoading();
        }
        console.error("获取穿搭列表网络错误:", err);
        // 如果网络错误，为安全起见，默认允许删除
        callback(false);
      }
    });
  },
  
  // 删除衣物
  deleteClothing: function() {
    const that = this;
    const id = this.data.clothingId;

    // 使用权限工具检查删除权限
    permission.checkPermissionAndExecute('clothes', id, 'delete', () => {
      // 先检查衣物是否在穿搭中使用
      this.checkClothingInOutfits((isUsed, outfitUsages) => {
      if (isUsed) {
        // 衣物在穿搭中使用，显示提示信息
        let outfitNames = outfitUsages.map(usage => usage.name).join('、');
        if (outfitUsages.length > 3) {
          // 如果穿搭太多，只显示前三个
          outfitNames = outfitUsages.slice(0, 3).map(usage => usage.name).join('、') + `等${outfitUsages.length}个穿搭`;
        }
        
        wx.showModal({
          title: '无法删除',
          content: `抱歉，该衣物存在于穿搭分类："${outfitNames}"中，如需删除请先删除相关穿搭或从穿搭中移除该衣物。`,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }
      
      // 衣物未在穿搭中使用，显示确认对话框
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这件衣物吗？删除后无法恢复。',
        confirmText: '删除',
        confirmColor: '#E64340',
        success(res) {
          if (res.confirm) {
            console.log("确认删除衣物，ID:", id);
            
            // 显示加载中
            wx.showLoading({
              title: '删除中...',
            });
            
            // 调用删除接口
            wx.request({
              url: `${app.globalData.apiBaseUrl}/delete_clothing.php`,
              method: 'POST',
              header: {
                'Authorization': app.globalData.token,
                'Content-Type': 'application/json'
              },
              data: {
                id: id
              },
              success: (res) => {
                wx.hideLoading();
                console.log("删除衣物响应:", res.data);
                
                if (res.statusCode === 200 && !res.data.error) {
                  // 删除成功
                  wx.showToast({
                    title: '删除成功',
                    icon: 'success'
                  });
                  
                  // 延迟返回上一页
                  setTimeout(() => {
                    that.navigateBack();
                  }, 1000);
                } else {
                  // 删除失败
                  wx.showToast({
                    title: res.data.msg || '删除失败',
                    icon: 'none'
                  });
                }
              },
              fail: (err) => {
                wx.hideLoading();
                console.error("删除衣物网络错误:", err);
                
                wx.showToast({
                  title: '网络错误，请稍后重试',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    });
    }); // 权限检查闭合
  },
  
  // 试穿此衣物
  tryOnClothing: function() {
    // 构建基本URL
    let url = `/pages/photos/select/index?select_mode=true&clothes_id=${this.data.clothingId}`;
    
    // 如果是商家模式，添加商家ID参数
    if (this.data.isMerchantMode && this.data.merchantId) {
      url += `&merchant_id=${this.data.merchantId}`;
    }
    
    // 跳转到照片选择页面
    wx.navigateTo({
      url: url,
      success: () => {
        console.log("成功跳转到照片选择页面");
      },
      fail: (err) => {
        console.error("跳转到照片选择页面失败:", err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 查看穿搭详情
  viewOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    if (!outfitId) {
      wx.showToast({
        title: '穿搭ID不存在',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/outfits/detail/detail?id=${outfitId}`,
      fail: (err) => {
        console.error('跳转到穿搭详情页失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
}) 