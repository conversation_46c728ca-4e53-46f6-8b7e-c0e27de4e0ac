<?php
/**
 * Add Clothing API
 * 
 * Adds a new clothing item for the user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - name: Clothing name (required)
 * - category: Category (required, one of: tops, pants, skirts, coats, shoes, bags, accessories)
 * - image_url: Image URL (required)
 * - tags: Comma-separated list of tags (optional)
 * - description: Additional properties as JSON (optional)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "id": 1,
 *     "name": "Black T-shirt",
 *     "category": "tops",
 *     "image_url": "https://example.com/image.jpg",
 *     "tags": "summer,casual",
 *     "description": "{\"color\":\"black\",\"brand\":\"Example\",\"price\":\"99\"}",
 *     "created_at": "2023-03-31 12:00:00"
 *   }
 * }
 */

require_once 'config.php';
require_once 'verify_token.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 处理Bearer前缀，与其他API保持一致
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7); // 去除 "Bearer " 前缀
}

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get JSON POST data
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if (!$data) {
    // If JSON parsing failed, try regular POST data
    $data = $_POST;
}

// Validate required fields
if (empty($data['name'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Name is required'
    ]);
    exit;
}

if (empty($data['category'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Category is required'
    ]);
    exit;
}

// Validate category
$db = new Database();
$conn = $db->getConnection();

// 检查分类是否有效（系统分类或用户自定义分类）
$validCategories = ['tops', 'pants', 'skirts', 'coats', 'shoes', 'bags', 'accessories'];
$isValidCategory = in_array($data['category'], $validCategories);

// 如果不是系统分类，检查是否是用户的自定义分类
if (!$isValidCategory) {
    $checkCategorySql = "SELECT id FROM clothing_categories 
                        WHERE code = :category 
                        AND (is_system = 1 OR user_id = :user_id)";
    $checkCategoryStmt = $conn->prepare($checkCategorySql);
    $checkCategoryStmt->bindParam(':category', $data['category'], PDO::PARAM_STR);
    $checkCategoryStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkCategoryStmt->execute();
    
    $isValidCategory = $checkCategoryStmt->rowCount() > 0;
}

if (!$isValidCategory) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid category'
    ]);
    exit;
}

if (empty($data['image_url'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Image URL is required'
    ]);
    exit;
}

// 初始化OSS辅助类
$ossHelper = new OssHelper();

// 检查图片URL是否已经是OSS URL
$imageUrl = $data['image_url'];

// 检查是否包含用户的抠图选择状态
$shouldUseSegmentedImage = isset($data['segment_enabled']) && $data['segment_enabled'] === true;
error_log("保存衣物时的抠图状态: " . ($shouldUseSegmentedImage ? '开启' : '关闭'));

// 处理URL，确保根据用户抠图选择使用正确的URL
if (strpos($imageUrl, '/segmented/') !== false && !$shouldUseSegmentedImage) {
    // 如果URL包含"segmented"标识但用户未选择抠图，尝试获取对应的原图URL
    error_log("检测到抠图URL，但用户未选择抠图，尝试获取原图URL: $imageUrl");
    
    // 尝试从URL中提取原图路径
    $originalUrl = str_replace('/segmented/segmented_', '/uploads/', $imageUrl);
    
    // 检查原图是否存在
    $originalUrlHeaders = @get_headers($originalUrl);
    if ($originalUrlHeaders && strpos($originalUrlHeaders[0], '200') !== false) {
        error_log("找到对应的原图URL: $originalUrl");
        $imageUrl = $originalUrl; // 使用原图URL
    } else {
        error_log("未找到对应的原图URL，继续使用当前URL");
    }
}

if (!$ossHelper->isOssUrl($imageUrl)) {
    // 不是OSS URL，需要下载到OSS
    error_log("将图片URL下载到OSS: $imageUrl");
    
    // 生成OSS路径
    $extension = pathinfo($imageUrl, PATHINFO_EXTENSION);
    if (empty($extension)) {
        // 如果URL中没有扩展名，默认使用jpg
        $extension = 'jpg';
    }
    
    $ossFilename = 'cloth_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $ossKey = OSS_PATH_CLOTHES . $ossFilename;
    
    // 下载远程图片到OSS
    $downloadResult = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);
    
    if ($downloadResult['success']) {
        // 使用OSS URL替换原始URL
        $imageUrl = $downloadResult['url'];
        error_log("图片已成功下载到OSS: $imageUrl");
    } else {
        error_log("下载图片到OSS失败: " . $downloadResult['error']);
        // 继续使用原始URL
    }
}

try {
    // 使用之前创建的数据库连接
    // $db和$conn已经在分类验证时创建了

    // Get current timestamp
    $now = date('Y-m-d H:i:s');
    
    // 检查是否是更新操作（有ID参数）
    $isUpdate = !empty($data['id']);
    
    if ($isUpdate) {
        // 记录执行信息
        error_log("准备更新衣物数据: 衣物ID={$data['id']}, 用户ID=$userId, 名称={$data['name']}, 类别={$data['category']}");
        
        // 首先检查该记录是否属于当前用户
        $checkSql = "SELECT id FROM clothes WHERE id = :id AND user_id = :user_id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute([
            'id' => $data['id'],
            'user_id' => $userId
        ]);
        
        if ($checkStmt->rowCount() === 0) {
            echo json_encode([
                'error' => true,
                'msg' => '没有权限更新此衣物或衣物不存在'
            ]);
            exit;
        }
        
        // 获取衣橱ID
        $wardrobeId = isset($data['wardrobe_id']) ? $data['wardrobe_id'] : null;
        
        // 如果提供了衣橱ID，记录日志
        if ($wardrobeId) {
            error_log("衣物将更新到衣橱ID: $wardrobeId");
        } else {
            error_log("未指定衣橱ID，衣物衣橱归属将不变");
        }
        
        // 执行更新操作
        $sql = "UPDATE clothes SET 
                name = :name, 
                category = :category, 
                image_url = :image_url, 
                tags = :tags, 
                description = :description, 
                updated_at = :updated_at";
        
        // 如果提供了衣橱ID，添加到更新字段中
        if ($wardrobeId !== null) {
            $sql .= ", wardrobe_id = :wardrobe_id";
        }
        
        $sql .= " WHERE id = :id AND user_id = :user_id";
        
        $stmt = $conn->prepare($sql);
        
        $params = [
            'id' => $data['id'],
            'user_id' => $userId,
            'name' => $data['name'],
            'category' => $data['category'],
            'image_url' => $imageUrl, // 使用可能更新后的OSS URL
            'tags' => isset($data['tags']) ? $data['tags'] : null,
            'description' => isset($data['description']) ? $data['description'] : null,
            'updated_at' => $now
        ];
        
        // 如果提供了衣橱ID，添加到参数中
        if ($wardrobeId !== null) {
            $params['wardrobe_id'] = $wardrobeId;
        }
        
        // 记录SQL和参数
        error_log("SQL: $sql");
        error_log("参数: " . json_encode($params));
        
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            error_log("成功更新衣物数据，ID: {$data['id']}");
            
            // 返回更新后的数据
            echo json_encode([
                'error' => false,
                'data' => [
                    'id' => $data['id'],
                    'name' => $data['name'],
                    'category' => $data['category'],
                    'image_url' => $imageUrl, // 使用可能更新后的OSS URL
                    'tags' => isset($data['tags']) ? $data['tags'] : null,
                    'description' => isset($data['description']) ? $data['description'] : null,
                    'updated_at' => $now
                ],
                'msg' => '衣物信息已更新'
            ]);
        } else {
            error_log("衣物数据未更新，可能ID不存在: {$data['id']}");
            echo json_encode([
                'error' => true,
                'msg' => '没有进行任何更改'
            ]);
        }
    } else {
        // 记录执行信息
        error_log("准备插入衣物数据: 用户ID=$userId, 名称={$data['name']}, 类别={$data['category']}");

        // 获取衣橱ID
        $wardrobeId = isset($data['wardrobe_id']) ? $data['wardrobe_id'] : null;
        
        // 如果提供了衣橱ID，记录日志
        if ($wardrobeId) {
            error_log("衣物将添加到衣橱ID: $wardrobeId");
        } else {
            error_log("未指定衣橱ID，衣物将添加到默认衣橱");
            
            // 查询用户的默认衣橱ID
            $defaultWardrobeSql = "SELECT id FROM wardrobes WHERE user_id = :user_id AND is_default = 1 LIMIT 1";
            $defaultWardrobeStmt = $conn->prepare($defaultWardrobeSql);
            $defaultWardrobeStmt->execute(['user_id' => $userId]);
            $defaultWardrobe = $defaultWardrobeStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($defaultWardrobe) {
                $wardrobeId = $defaultWardrobe['id'];
                error_log("找到默认衣橱ID: $wardrobeId");
            } else {
                error_log("未找到默认衣橱，衣物将不关联到任何衣橱");
            }
        }

        // 执行插入操作
        $sql = "INSERT INTO clothes (user_id, name, category, image_url, tags, description, created_at, updated_at, wardrobe_id) 
                VALUES (:user_id, :name, :category, :image_url, :tags, :description, :created_at, :updated_at, :wardrobe_id)";
        
        $stmt = $conn->prepare($sql);
        
        $params = [
            'user_id' => $userId,
            'name' => $data['name'],
            'category' => $data['category'],
            'image_url' => $imageUrl, // 使用可能更新后的OSS URL
            'tags' => isset($data['tags']) ? $data['tags'] : null,
            'description' => isset($data['description']) ? $data['description'] : null,
            'created_at' => $now,
            'updated_at' => $now,
            'wardrobe_id' => $wardrobeId
        ];
        
        // 记录SQL和参数
        error_log("SQL: $sql");
        error_log("参数: " . json_encode($params));
        
        $stmt->execute($params);

        // Get the new clothing ID
        $clothingId = $conn->lastInsertId();
        error_log("成功插入衣物数据，新ID: $clothingId");

        // Return the newly created clothing data
        echo json_encode([
            'error' => false,
            'data' => [
                'id' => $clothingId,
                'name' => $data['name'],
                'category' => $data['category'],
                'image_url' => $imageUrl, // 使用可能更新后的OSS URL
                'tags' => isset($data['tags']) ? $data['tags'] : null,
                'description' => isset($data['description']) ? $data['description'] : null,
                'created_at' => $now
            ]
        ]);
    }
} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("数据库错误码: $errorCode");
    error_log("数据库错误信息: $errorMessage");
    
    // 检查是否是外键约束错误
    if(strpos($errorMessage, 'foreign key constraint') !== false || $errorCode == 23000) {
        // 记录更多诊断信息
        error_log("可能的外键约束错误 - 检查用户 ID: $userId 是否存在于users表中");
        echo json_encode([
            'error' => true,
            'msg' => '外键约束错误：用户记录可能不存在，请先登录或创建用户账户'
        ]);
    } else {
        // 返回通用错误，但带有更多信息
        echo json_encode([
            'error' => true,
            'msg' => "数据库错误: $errorCode - " . substr($errorMessage, 0, 100)
        ]);
    }
} catch (Exception $e) {
    // 其他错误
    error_log("添加/更新衣物失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '添加/更新衣物失败: ' . $e->getMessage()
    ]);
}
?> 