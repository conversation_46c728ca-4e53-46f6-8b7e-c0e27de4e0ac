<view class="container">
  <view class="form-header">
    <view class="step-indicator">
      <view class="step completed">1</view>
      <view class="step-line completed"></view>
      <view class="step active">2</view>
      <view class="step-line"></view>
      <view class="step">3</view>
    </view>
    <view class="step-text">选择获取方式</view>
  </view>
  
  <view class="content">
    <!-- 所有获取方式 -->
    <view class="access-options-grid">
      <!-- 分享选项 -->
      <view class="option-card share-card">
        <view class="option-icon">🎁</view>
        <view class="option-title">分享好友免费兑换</view>
        <view class="option-desc">分享给好友即可获得一次免费形象分析机会</view>
        <button class="option-btn share-action" open-type="share">立即分享</button>
      </view>

      <!-- 邀请码选项 -->
      <view class="option-card invite-card">
        <view class="option-icon">🔑</view>
        <view class="option-title">邀请码兑换</view>
        <view class="option-desc">使用有效邀请码立即获得形象分析服务</view>
        <button class="option-btn invite-action" bindtap="showInvitationCodeModal">输入邀请码</button>
      </view>
      
      <!-- 直接购买选项 -->
      <view class="option-card purchase-card">
        <view class="option-icon">💰</view>
        <view class="option-title">直接购买</view>
        <view class="option-desc">支付9.9元获得专业形象分析服务</view>
        <button class="option-btn purchase-action" bindtap="payNow">立即购买</button>
      </view>
    </view>
  </view>

  <!-- 邀请码弹窗 -->
  <view class="invitation-modal" wx:if="{{showInvitationCodeModal}}">
    <view class="invitation-modal-mask" bindtap="hideInvitationCodeModal"></view>
    <view class="invitation-modal-content">
      <view class="invitation-modal-header">
        <text>输入邀请码</text>
      </view>
      <view class="invitation-modal-body">
        <input class="invitation-code-input" placeholder="请输入邀请码" model:value="{{invitationCode}}" maxlength="20" />
        <text class="invitation-modal-tip">输入有效邀请码可立即兑换形象分析服务</text>
      </view>
      <view class="invitation-modal-footer">
        <button class="cancel-btn" bindtap="hideInvitationCodeModal">取消</button>
        <button class="confirm-btn" bindtap="submitInvitationCode">确认兑换</button>
      </view>
    </view>
  </view>
</view> 