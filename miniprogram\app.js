// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    apiBaseUrl: 'https://cyyg.alidog.cn/login_backend',
    baseUrl: 'https://cyyg.alidog.cn/login_backend', // 添加baseUrl以便新的API使用
    tokenVerificationRetryCount: 0,
    maxTokenVerificationRetries: 3,
    useMockUser: false, // 标记是否使用模拟用户
    isLoggedIn: false, // 添加登录状态标记
    needRefreshClothes: false, // 标记是否需要刷新衣物列表
    needRefreshWardrobes: false, // 标记是否需要刷新衣橱列表
    needRefreshOutfits: false, // 标记是否需要刷新穿搭列表
    needRefreshOutfitCategories: false, // 标记是否需要刷新穿搭分类列表
    showClothingName: false, // 是否显示衣物名称
    needRefreshTags: false, // 是否需要刷新标签列表
  },
  
  onLaunch: function () {
    // 获取本地存储的 token 和 userInfo
    const token = wx.getStorageSync('token');
    const userInfoStr = wx.getStorageSync('userInfo');
    
    // 获取显示名称设置
    const showClothingName = wx.getStorageSync('showClothingName');
    // 确保显示名称设置是一个布尔值
    this.globalData.showClothingName = showClothingName === true;
    console.log('启动时加载显示名称设置:', this.globalData.showClothingName);
    
    if (token) {
      this.globalData.token = token;
      
      // 如果token是模拟token，设置useMockUser
      this.globalData.useMockUser = token.startsWith('mock_');
      this.globalData.isLoggedIn = !this.globalData.useMockUser; // 设置登录状态
      
      // 如果有用户信息，先加载到全局
      if (userInfoStr) {
        try {
          this.globalData.userInfo = JSON.parse(userInfoStr);
        } catch (e) {
          console.error("解析存储的用户信息失败:", e);
        }
      }
      
      // 验证 token 有效性
      this.checkTokenValidity(token);
    } else {
      // 没有token，创建模拟token使用体验账号
      console.log("未检测到登录状态，将使用体验账号");
      const mockToken = this.createMockToken();
      this.globalData.token = mockToken;
      this.globalData.useMockUser = true;
      this.globalData.isLoggedIn = false; // 模拟用户不算登录状态
      
      // 验证模拟token
      this.checkTokenValidity(mockToken);
    }
  },
  
  // 创建模拟token
  createMockToken: function() {
    // 创建一个以mock_开头的token，后端会识别这种格式并返回ID为1的用户
    const mockToken = 'mock_token_' + Date.now();
    console.log("创建模拟token:", mockToken);
    return mockToken;
  },
  
  // Check if the stored token is still valid
  checkTokenValidity: function (token, callback) {
    console.log("验证token有效性:", token);
    
    wx.request({
      url: `${this.globalData.apiBaseUrl}/verify_token.php`,
      method: 'GET',
      header: {
        'Authorization': token
      },
      success: (res) => {
        console.log("验证token响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // token有效，更新用户信息
          this.globalData.userInfo = res.data.data;
          console.log("token有效，用户信息已更新:", this.globalData.userInfo);
          
          // 重置重试计数
          this.globalData.tokenVerificationRetryCount = 0;
          
          // 确保本地存储的用户信息与全局同步
          wx.setStorageSync('userInfo', JSON.stringify(this.globalData.userInfo));
          
          // 如果是模拟token，不存储到本地，设置登录状态
          if (!this.globalData.useMockUser) {
            wx.setStorageSync('token', token);
            this.globalData.isLoggedIn = true;
          } else {
            this.globalData.isLoggedIn = false;
          }
          
          // 设置需要刷新数据的标记，确保数据为最新
          this.globalData.needRefreshClothes = true;
          this.globalData.needRefreshWardrobes = true;
          this.globalData.needRefreshOutfits = true;
          this.globalData.needRefreshOutfitCategories = true;
          
          // 调用回调函数
          if (typeof callback === 'function') {
            callback(true, this.globalData.userInfo);
          }
        } else {
          console.warn("token验证不通过，尝试重试");
          
          // 增加重试次数
          this.globalData.tokenVerificationRetryCount++;
          
          // 如果未超过最大重试次数，则重试
          if (this.globalData.tokenVerificationRetryCount < this.globalData.maxTokenVerificationRetries) {
            console.log(`尝试第 ${this.globalData.tokenVerificationRetryCount} 次重新验证token`);
            
            // 延迟 2 秒后重试
            setTimeout(() => {
              this.checkTokenValidity(token, callback);
            }, 2000);
            
            return;
          }
          
          console.warn(`token经过 ${this.globalData.maxTokenVerificationRetries} 次验证均无效，需要重新登录`);
          
          // 如果是真实token无效，清除存储并尝试使用模拟token
          if (!this.globalData.useMockUser) {
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            this.globalData.token = null;
            this.globalData.userInfo = null;
            this.globalData.isLoggedIn = false;
            
            // 创建模拟token使用体验账号
            console.log("真实token无效，切换到体验账号");
            const mockToken = this.createMockToken();
            this.globalData.token = mockToken;
            this.globalData.useMockUser = true;
            
            // 验证模拟token
            this.checkTokenValidity(mockToken, callback);
          } else {
            // 如果连模拟token也无效，说明可能是服务器问题
            console.error("模拟token也无效，可能是服务器问题");
            this.globalData.token = null;
            this.globalData.userInfo = null;
            this.globalData.isLoggedIn = false;
            
            // 调用回调函数
            if (typeof callback === 'function') {
              callback(false);
            }
          }
          
          // 重置重试计数
          this.globalData.tokenVerificationRetryCount = 0;
        }
      },
      fail: (err) => {
        console.error("验证token请求失败:", err);
        
        // 增加重试次数
        this.globalData.tokenVerificationRetryCount++;
        
        // 如果未超过最大重试次数，则重试
        if (this.globalData.tokenVerificationRetryCount < this.globalData.maxTokenVerificationRetries) {
          console.log(`网络请求失败，尝试第 ${this.globalData.tokenVerificationRetryCount} 次重新验证token`);
          
          // 延迟 3 秒后重试
          setTimeout(() => {
            this.checkTokenValidity(token, callback);
          }, 3000);
          
          return;
        }
        
        console.warn("网络错误多次重试后仍然失败");
        
        // 重置重试计数
        this.globalData.tokenVerificationRetryCount = 0;
        
        // 网络错误持续存在，但我们不清除token，让用户可以继续使用app
        // 只标记网络连接问题，下次启动app再尝试验证
        this.globalData.networkError = true;
        
        // 调用回调函数
        if (typeof callback === 'function') {
          callback(false);
        }
      }
    });
  },
  
  // Login with WeChat
  login: function (callback) {
    console.log("开始微信登录流程");
    
    wx.login({
      success: (res) => {
        console.log("获取微信登录code成功：", res.code);
        
        if (res.code) {
          // 发送code到后端
          wx.request({
            url: `${this.globalData.apiBaseUrl}/login.php`,
            method: 'POST',
            data: {
              code: res.code
            },
            success: (result) => {
              console.log("登录接口返回：", result);
              
              if (result.statusCode === 200 && !result.data.error) {
                const token = result.data.data.token;
                const isNewUser = result.data.data.is_new_user;
                const userId = result.data.data.user_id;
                
                console.log("登录成功，获取到token:", token);
                console.log("用户ID:", userId, "是否新用户:", isNewUser);
                
                // 清除之前的登录状态
                wx.removeStorageSync('userInfo');
                this.globalData.userInfo = null;
                this.globalData.useMockUser = false; // 重置模拟用户状态
                this.globalData.isLoggedIn = true; // 设置为已登录状态
                
                // 保存新token
                wx.setStorageSync('token', token);
                this.globalData.token = token;
                
                // 设置需要刷新数据的标记
                this.globalData.needRefreshClothes = true;
                this.globalData.needRefreshWardrobes = true;
                this.globalData.needRefreshOutfits = true; // 添加穿搭刷新标记
                this.globalData.needRefreshOutfitCategories = true; // 添加穿搭分类刷新标记
                
                // 验证token并获取用户信息
                this.checkTokenValidity(token, (success, userInfo) => {
                  if (success) {
                    if (typeof callback === 'function') {
                      callback({
                        success: true,
                        isNewUser: isNewUser
                      });
                    }
                  } else {
                    if (typeof callback === 'function') {
                      callback({
                        success: false,
                        error: '获取用户信息失败'
                      });
                    }
                  }
                });
              } else {
                console.error("登录失败:", result.data);
                
                // 登录失败，使用模拟token
                if (!this.globalData.useMockUser) {
                  console.log("登录失败，切换到体验账号");
                  const mockToken = this.createMockToken();
                  this.globalData.token = mockToken;
                  this.globalData.useMockUser = true;
                  this.globalData.isLoggedIn = false;
                  
                  // 验证模拟token
                  this.checkTokenValidity(mockToken);
                }
                
                if (typeof callback === 'function') {
                  callback({
                    success: false,
                    error: result.data.msg || '登录失败'
                  });
                }
              }
            },
            fail: (err) => {
              console.error("登录请求失败:", err);
              
              // 尝试使用模拟token
              if (!this.globalData.useMockUser) {
                console.log("登录请求失败，切换到体验账号");
                const mockToken = this.createMockToken();
                this.globalData.token = mockToken;
                this.globalData.useMockUser = true;
                this.globalData.isLoggedIn = false;
                
                // 验证模拟token
                this.checkTokenValidity(mockToken);
              }
              
              if (typeof callback === 'function') {
                callback({
                  success: false,
                  error: '网络错误，请稍后重试'
                });
              }
            }
          });
        } else {
          console.error("获取微信登录code失败");
          
          // 尝试使用模拟token
          if (!this.globalData.useMockUser) {
            console.log("获取登录code失败，切换到体验账号");
            const mockToken = this.createMockToken();
            this.globalData.token = mockToken;
            this.globalData.useMockUser = true;
            this.globalData.isLoggedIn = false;
            
            // 验证模拟token
            this.checkTokenValidity(mockToken);
          }
          
          if (typeof callback === 'function') {
            callback({
              success: false,
              error: '获取微信登录码失败'
            });
          }
        }
      },
      fail: (err) => {
        console.error("wx.login调用失败:", err);
        
        // 尝试使用模拟token
        if (!this.globalData.useMockUser) {
          console.log("wx.login调用失败，切换到体验账号");
          const mockToken = this.createMockToken();
          this.globalData.token = mockToken;
          this.globalData.useMockUser = true;
          this.globalData.isLoggedIn = false;
          
          // 验证模拟token
          this.checkTokenValidity(mockToken);
        }
        
        if (typeof callback === 'function') {
          callback({
            success: false,
            error: '微信登录失败'
          });
        }
      }
    });
  },
  
  // Update user profile
  updateUserInfo: function (userInfo, callback) {
    if (!this.globalData.token) {
      if (typeof callback === 'function') {
        callback({
          success: false,
          error: '未登录'
        });
      }
      return;
    }
    
    // 如果使用的是模拟账号，不允许更新资料
    if (this.globalData.useMockUser) {
      console.log("体验账号不支持更新资料");
      if (typeof callback === 'function') {
        callback({
          success: false,
          error: '体验账号不支持更新资料'
        });
      }
      return;
    }
    
    console.log("更新用户信息:", userInfo);
    
    // 检查头像URL是否为OSS或CDN链接
    const isOssOrCdnUrl = this.isOssOrCdnUrl(userInfo.avatarUrl);
    
    // 如果不是OSS或CDN链接，视为临时文件需要先上传
    if (!isOssOrCdnUrl) {
      console.log("检测到非OSS/CDN头像URL，可能是临时文件，开始上传到OSS:", userInfo.avatarUrl);
      
      wx.showLoading({
        title: '上传头像中...',
        mask: true
      });
      
      // 调用upload_image.php上传头像
      wx.uploadFile({
        url: `${this.globalData.apiBaseUrl}/upload_image.php`,
        filePath: userInfo.avatarUrl,
        name: 'image',
        header: {
          'Authorization': this.globalData.token
        },
        formData: {
          'segment_image': 'false' // 不需要抠图
        },
        success: (uploadRes) => {
          wx.hideLoading();
          
          console.log("头像上传响应:", uploadRes);
          let response;
          
          try {
            // 解析JSON响应
            response = JSON.parse(uploadRes.data);
          } catch (e) {
            console.error("解析上传响应失败:", e);
            if (typeof callback === 'function') {
              callback({
                success: false,
                error: '头像上传失败，无法解析响应'
              });
            }
            return;
          }
          
          if (response && !response.error && response.data && response.data.image_url) {
            // 上传成功，获取到CDN/OSS URL
            const ossAvatarUrl = response.data.image_url;
            console.log("头像上传成功，OSS URL:", ossAvatarUrl);
            
            // 使用OSS URL更新用户信息
            this.updateUserProfileWithUrl({
              ...userInfo,
              avatarUrl: ossAvatarUrl
            }, callback);
          } else {
            console.error("头像上传失败:", response ? response.msg : '未知错误');
            if (typeof callback === 'function') {
              callback({
                success: false,
                error: response ? response.msg : '头像上传失败'
              });
            }
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error("头像上传请求失败:", err);
          if (typeof callback === 'function') {
            callback({
              success: false,
              error: '头像上传失败，请重试'
            });
          }
        }
      });
    } else {
      // 已经是OSS/CDN链接，直接更新用户信息
      console.log("头像URL已是OSS/CDN链接，直接更新用户信息");
      this.updateUserProfileWithUrl(userInfo, callback);
    }
  },
  
  // 判断URL是否为OSS或CDN链接
  isOssOrCdnUrl: function(url) {
    if (!url) return false;
    
    // 检查是否包含OSS域名
    const ossDomain = 'cyymj.oss-cn-shanghai.aliyuncs.com';
    // 检查是否包含CDN域名
    const cdnDomain = 'images.alidog.cn';
    
    // 如果URL包含OSS域名或CDN域名，则视为OSS/CDN链接
    return url.indexOf(ossDomain) !== -1 || url.indexOf(cdnDomain) !== -1;
  },
  
  // 带URL的用户资料更新（内部使用）
  updateUserProfileWithUrl: function (userInfo, callback) {
    // 发送请求更新用户信息
    wx.request({
      url: `${this.globalData.apiBaseUrl}/update_profile.php`,
      method: 'POST',
      header: {
        'Authorization': this.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender || 0
      },
      success: (res) => {
        console.log("更新用户信息响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 更新本地用户信息缓存
          const updatedUserInfo = {
            nickname: userInfo.nickName,
            avatar_url: userInfo.avatarUrl,
            gender: userInfo.gender || 0
          };
          
          this.globalData.userInfo = {
            ...this.globalData.userInfo,
            ...updatedUserInfo
          };
          
          // 保存到本地存储
          wx.setStorageSync('userInfo', JSON.stringify(this.globalData.userInfo));
          console.log("用户信息已更新:", this.globalData.userInfo);
          
          if (typeof callback === 'function') {
            callback({
              success: true
            });
          }
        } else {
          console.error("更新用户信息失败:", res.data);
          
          if (typeof callback === 'function') {
            callback({
              success: false,
              error: res.data.msg || '更新资料失败'
            });
          }
        }
      },
      fail: (err) => {
        console.error("更新用户信息请求失败:", err);
        
        if (typeof callback === 'function') {
          callback({
            success: false,
            error: '网络错误，请稍后重试'
          });
        }
      }
    });
  },
  
  // 清除所有本地存储数据，用于重置应用状态
  clearStorage: function() {
    console.log("清除所有本地存储数据");
    
    // 删除特定的登录相关存储，而不是清除所有本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 重置全局数据
    this.globalData.token = null;
    this.globalData.userInfo = null;
    this.globalData.tokenVerificationRetryCount = 0;
    this.globalData.networkError = false;
    this.globalData.useMockUser = false;
    this.globalData.needRefreshClothes = false;
    
    console.log("登录数据已清除");
    
    // 创建模拟token使用体验账号
    console.log("切换到体验账号");
    const mockToken = this.createMockToken();
    this.globalData.token = mockToken;
    this.globalData.useMockUser = true;
    
    // 验证模拟token
    this.checkTokenValidity(mockToken);
    
    // 不再重定向到登录页面，而是留在当前页面继续体验
    // wx.reLaunch({
    //   url: '/pages/login/login'
    // });
  },
  
  // 穿搭数据管理函数
  
  // 获取穿搭列表
  getOutfits: function(callback, page = 1, per_page = 20) {
    console.log('获取穿搭列表', '页码:', page, '每页数量:', per_page);
    // 默认先从本地存储获取
    const localOutfits = this.getLocalOutfits();
    console.log('本地穿搭数据:', localOutfits.length, '条');
    
    // 如果没有token，不能从服务器获取，仅使用本地数据
    if (!this.globalData.token) {
      console.log('未登录，仅返回本地穿搭数据');
      if (typeof callback === 'function') {
        // 使用setTimeout确保回调函数总是异步执行，保持一致性
        setTimeout(() => {
          callback(localOutfits);
        }, 0);
      }
      return localOutfits;
    }
    
    // 从服务器获取（包括体验账号）
    console.log(this.globalData.useMockUser ? '体验账号从服务器获取穿搭数据' : '从服务器获取穿搭数据');
    wx.request({
      url: `${this.globalData.apiBaseUrl}/get_outfits.php`,
      method: 'GET',
      header: {
        'Authorization': this.globalData.token
      },
      data: {
        page: page,
        per_page: per_page
      },
      success: (res) => {
        console.log('获取穿搭列表响应:', res.statusCode, res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const serverOutfits = res.data.data || [];
          console.log('从服务器获取到', serverOutfits.length, '条穿搭数据');
          
          // 从分页信息中获取是否有更多数据
          const pagination = res.data.pagination || {};
          const currentPage = pagination.current_page || page;
          const totalPages = pagination.total_pages || 1;
          const hasMore = currentPage < totalPages;
          
          console.log('分页信息:', '当前页:', currentPage, '总页数:', totalPages, '是否有更多:', hasMore);
          
          // 处理服务器返回的数据，确保ID格式一致
          const processedServerOutfits = serverOutfits.map(outfit => {
            // 确保ID是字符串
            const outfitId = outfit.id.toString();
            return {
              id: outfitId,
              name: outfit.name || '',
              description: outfit.description || '',
              thumbnail: outfit.thumbnail || '',
              category_id: outfit.category_id ? String(outfit.category_id) : null, // 确保分类ID为字符串
              category_name: outfit.category_name || null, // 添加分类名称
              items: outfit.items || [],
              created_at: outfit.created_at || new Date().toISOString(),
              updated_at: outfit.updated_at || new Date().toISOString(),
              is_public: outfit.is_public !== undefined ? outfit.is_public : 0, // 保留公开状态
              likes_count: outfit.likes_count !== undefined ? parseInt(outfit.likes_count) : 0, // 保留点赞数
              user_id: outfit.user_id || '' // 保留用户ID
            };
          });
          
          // 如果是第一页，完全替换本地数据
          // 如果不是第一页，则只保存本次获取的数据用于返回给回调，不影响本地存储
          if (page === 1) {
            this.saveLocalOutfits(processedServerOutfits);
            console.log('服务器穿搭数据已保存到本地');
          }
          
          if (typeof callback === 'function') {
            callback(processedServerOutfits, hasMore);
          }
        } else {
          console.error('获取穿搭列表失败:', res.data);
          
          if (typeof callback === 'function') {
            callback(localOutfits, false); // 失败时返回本地数据，并指示没有更多数据
          }
        }
      },
      fail: (err) => {
        console.error('获取穿搭请求失败:', err);
        
        if (typeof callback === 'function') {
          callback(localOutfits, false); // 失败时返回本地数据，并指示没有更多数据
        }
      }
    });
    
    // 同时返回本地数据用于立即显示
    return localOutfits;
  },
  
  // 从本地获取穿搭列表
  getLocalOutfits: function() {
    try {
      const outfitsStr = wx.getStorageSync('outfits');
      return outfitsStr ? JSON.parse(outfitsStr) : [];
    } catch (e) {
      console.error('获取本地穿搭数据失败:', e);
      return [];
    }
  },
  
  // 保存穿搭列表到本地
  saveLocalOutfits: function(outfits) {
    try {
      wx.setStorageSync('outfits', JSON.stringify(outfits));
      this.globalData.needRefreshOutfits = true;
    } catch (e) {
      console.error('保存穿搭数据到本地失败:', e);
    }
  },
  
  // 添加或更新穿搭
  saveOutfit: function(outfit, callback) {
    console.log('开始保存穿搭:', outfit.id, outfit.name);

    // 检查穿搭是否为空白穿搭（没有添加任何衣物项）
    const outfitItems = outfit.items || [];
    const isEmptyOutfit = outfitItems.length === 0;
    const forceAdd = outfit.forceAdd || false; // 是否强制添加，即使为空

    // 如果是空白穿搭，且不是强制添加，则不保存
    if (isEmptyOutfit && !forceAdd) {
      console.log('穿搭为空且未标记强制保存，跳过保存操作');
      if (callback && typeof callback === 'function') {
        callback({
          success: false,
          error: '穿搭没有添加任何衣物，不进行保存'
        });
      }
      return;
    }
    
    // 首先保存到本地
    const outfits = this.getLocalOutfits();
    const index = outfits.findIndex(item => item.id === outfit.id);
    
    // 确保创建和更新时间
    if (!outfit.created_at) {
      outfit.created_at = new Date().toISOString();
    }
    outfit.updated_at = new Date().toISOString();
    
    if (index !== -1) {
      // 更新现有穿搭
      outfits[index] = outfit;
    } else {
      // 添加新穿搭
      outfits.push(outfit);
    }
    
    this.saveLocalOutfits(outfits);
    
    // 准备发送到服务器的数据
    // 提取ID中的数字部分，如果有'outfit-'前缀
    let serverOutfitId = outfit.id;
    if (typeof outfit.id === 'string' && outfit.id.startsWith('outfit-')) {
      // 尝试提取数字部分，如果ID是服务器返回的数字ID，则使用原ID
      const numericId = outfit.id.split('outfit-')[1];
      if (numericId && !isNaN(numericId)) {
        serverOutfitId = numericId;
      }
    }
    
    // 处理包含临时衣物的穿搭
    const processedItems = outfit.items ? outfit.items.map(item => {
      // 检查是否为临时衣物（ID以temp_开头）
      if (item.clothing_id && typeof item.clothing_id === 'string' && 
          item.clothing_id.startsWith('temp_')) {
        console.log('处理临时衣物项:', item.clothing_id);
        // 确保临时衣物的数据结构正确
        return {
          ...item,
          // 确保临时衣物ID保持不变
          clothing_id: item.clothing_id
        };
      }
      return item; // 非临时衣物不变
    }) : [];
    
    // 构建服务器请求数据
    const serverData = {
      id: serverOutfitId,
      name: outfit.name,
      description: outfit.description || '',
      thumbnail: outfit.thumbnail || '',
      items: processedItems.length > 0 ? processedItems : outfit.items || [],
      category_id: outfit.category_id || null, // 添加分类ID字段
      is_public: outfit.is_public !== undefined ? outfit.is_public : 0, // 保留公开状态
      likes_count: outfit.likes_count !== undefined ? outfit.likes_count : 0 // 保留点赞数
    };
    
    // 如果是体验账号，标记数据来源
    if (this.globalData.useMockUser) {
      serverData.is_demo = true; // 添加标记，表明这是体验账号数据
    }
    
    // 如果没有登录或使用模拟用户，只保存到本地，但也尝试同步到服务器
    if (!this.globalData.token || this.globalData.useMockUser) {
      console.log('未登录或使用模拟用户，穿搭仅保存到本地');
      
      // 体验账号也尝试同步到服务器
      if (this.globalData.token && this.globalData.useMockUser) {
        console.log('体验账号尝试同步穿搭到服务器');
        this.syncOutfitToServer(this.globalData.token, serverData, outfit, callback);
      } else {
        // 无token时直接返回成功
        if (typeof callback === 'function') {
          // 使用setTimeout确保回调函数总是异步执行，保持一致性
          setTimeout(() => {
            callback({
              success: true,
              data: outfit
            });
          }, 0);
        }
      }
      return outfit;
    }
    
    // 同步到服务器（正常登录用户）
    this.syncOutfitToServer(this.globalData.token, serverData, outfit, callback);
    
    return outfit;
  },
  
  // 将穿搭同步到服务器的公共方法
  syncOutfitToServer: function(token, serverData, originalOutfit, callback) {
    console.log('发送到服务器的穿搭数据:', serverData);
    
    wx.request({
      url: `${this.globalData.apiBaseUrl}/save_outfit.php`,
      method: 'POST',
      header: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      data: serverData,
      success: (res) => {
        console.log('保存穿搭响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          console.log('穿搭保存成功:', res.data);
          
          // 如果服务器返回了更新后的穿搭（可能包含服务器生成的ID），更新本地
          if (res.data.data) {
            const updatedOutfit = res.data.data;
            
            // 构建更新后的本地穿搭对象
            const localUpdatedOutfit = {
              id: updatedOutfit.id.toString(), // 确保ID是字符串
              name: updatedOutfit.name,
              description: updatedOutfit.description || '',
              thumbnail: updatedOutfit.thumbnail || '',
              category_id: updatedOutfit.category_id || originalOutfit.category_id, // 保留分类ID
              category_name: updatedOutfit.category_name, // 添加分类名称（如果有）
              items: updatedOutfit.items || originalOutfit.items, // 使用返回的items或保留原来的
              created_at: updatedOutfit.created_at || originalOutfit.created_at,
              updated_at: updatedOutfit.updated_at || new Date().toISOString()
            };
            
            console.log('更新本地穿搭数据:', localUpdatedOutfit);
            
            // 更新本地数据
            const localOutfits = this.getLocalOutfits();
            const idx = localOutfits.findIndex(item => item.id === originalOutfit.id);
            if (idx !== -1) {
              localOutfits[idx] = localUpdatedOutfit;
            } else {
              // 如果找不到原ID，可能是因为ID已变更，添加为新条目
              localOutfits.push(localUpdatedOutfit);
            }
            this.saveLocalOutfits(localOutfits);
            
            if (typeof callback === 'function') {
              callback({
                success: true,
                data: localUpdatedOutfit
              });
            }
          } else {
            // 服务器没有返回更新的数据，使用原始数据
            if (typeof callback === 'function') {
              callback({
                success: true,
                data: originalOutfit
              });
            }
          }
        } else {
          console.error('保存穿搭失败:', res.data);
          
          if (typeof callback === 'function') {
            callback({
              success: false,
              error: res.data.msg || '保存失败'
            });
          }
        }
      },
      fail: (err) => {
        console.error('保存穿搭请求失败:', err);
        
        if (typeof callback === 'function') {
          callback({
            success: false,
            error: '网络错误，请稍后重试'
          });
        }
      }
    });
  },
  
  // 删除穿搭
  deleteOutfit: function(outfitId, callback) {
    console.log('开始删除穿搭:', outfitId);
    // 先从本地删除
    let outfits = this.getLocalOutfits();
    outfits = outfits.filter(item => item.id !== outfitId);
    this.saveLocalOutfits(outfits);
    console.log('已从本地删除穿搭');
    
    // 如果没有token，只能本地删除
    if (!this.globalData.token) {
      console.log('未登录，仅删除本地穿搭数据');
      if (typeof callback === 'function') {
        // 使用setTimeout确保回调函数总是异步执行，保持一致性
        setTimeout(() => {
          callback({ success: true });
        }, 0);
      }
      return;
    }
    
    // 处理ID格式，提取数字部分
    let serverOutfitId = outfitId;
    if (typeof outfitId === 'string' && outfitId.startsWith('outfit-')) {
      const numericId = outfitId.split('outfit-')[1];
      if (numericId && !isNaN(numericId)) {
        serverOutfitId = numericId;
      }
    }
    
    console.log(this.globalData.useMockUser ? 
      '体验账号向服务器发送删除请求，ID:' : 
      '向服务器发送删除请求，ID:', serverOutfitId);
    
    // 准备请求数据
    const requestData = { id: serverOutfitId };
    
    // 如果是体验账号，添加标记
    if (this.globalData.useMockUser) {
      requestData.is_demo = true;
    }
    
    // 从服务器删除（包括体验账号）
    wx.request({
      url: `${this.globalData.apiBaseUrl}/delete_outfit.php`,
      method: 'POST',
      header: {
        'Authorization': this.globalData.token,
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('删除穿搭响应:', res.statusCode, res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          console.log('穿搭删除成功:', res.data);
          
          if (typeof callback === 'function') {
            callback({ success: true });
          }
        } else {
          console.error('删除穿搭失败:', res.data);
          
          // 尝试恢复本地数据
          this.getOutfits();
          
          if (typeof callback === 'function') {
            callback({
              success: false,
              error: res.data.msg || '删除失败'
            });
          }
        }
      },
      fail: (err) => {
        console.error('删除穿搭请求失败:', err);
        
        // 尝试恢复本地数据
        this.getOutfits();
        
        if (typeof callback === 'function') {
          callback({
            success: false,
            error: '网络错误，请稍后重试'
          });
        }
      }
    });
  },
  
  // 检查页面是否存在
  checkPageExists: function(pagePath) {
    try {
      // 标准化页面路径格式
      if (pagePath.startsWith('/')) {
        pagePath = pagePath.substring(1);
      }
      
      // 尝试从__wxConfig中获取页面列表
      if (this.__wxConfig && this.__wxConfig.pages) {
        return this.__wxConfig.pages.includes(pagePath);
      }
      
      // 如果无法从__wxConfig获取，尝试从app.json中读取
      // 注意：这种方式在实际运行环境可能不可靠，仅作为备选
      const appConfig = wx.getStorageSync('appConfig');
      if (appConfig && appConfig.pages) {
        return appConfig.pages.includes(pagePath);
      }
      
      // 如果都无法获取，返回undefined表示无法确定
      return undefined;
    } catch (error) {
      console.error('检查页面是否存在时出错:', error);
      return undefined;
    }
  },
  
  // 安全的页面跳转函数
  navigateToPage: function(pagePath, options = {}) {
    try {
      // 日志
      console.log('准备跳转到页面:', pagePath);
      
      // 标准化页面路径
      if (!pagePath.startsWith('/')) {
        pagePath = '/' + pagePath;
      }
      
      // 构建完整URL
      let url = pagePath;
      if (options.query) {
        const queryString = Object.keys(options.query)
          .map(key => `${key}=${encodeURIComponent(options.query[key])}`)
          .join('&');
        url = `${pagePath}?${queryString}`;
      }
      
      // 检查是否为分享跳转
      const isShareNavigation = options.query && options.query.source === 'outfit_share';
      
      // 如果是分享链接且目标是特定页面，则直接跳转到首页
      if (isShareNavigation && pagePath.includes('/pages/smart_outfit/smart_outfit')) {
        console.log('检测到分享链接访问穿搭推荐页面，重定向到首页');
        wx.switchTab({
          url: '/pages/index/index'
        });
        return;
      }
      
      // 执行跳转
      wx.navigateTo({
        url: url,
        success: (res) => {
          console.log('页面跳转成功');
          if (options.success) options.success(res);
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          
          // 尝试诊断问题
          let errorMsg = '无法打开页面';
          
          // 检查页面是否注册
          const pageExists = this.checkPageExists(pagePath.substring(1));
          if (pageExists === false) {
            errorMsg = '页面不存在';
          } else if (err.errMsg && err.errMsg.indexOf('limit exceed') > -1) {
            errorMsg = '页面层级过多';
          } else if (err.errMsg) {
            errorMsg = err.errMsg;
          }
          
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
          
          if (options.fail) options.fail(err);
        },
        complete: options.complete
      });
    } catch (error) {
      console.error('执行页面跳转时出错:', error);
      wx.showToast({
        title: '系统错误',
        icon: 'none',
        duration: 2000
      });
      if (options.fail) options.fail(error);
    }
  }
}) 