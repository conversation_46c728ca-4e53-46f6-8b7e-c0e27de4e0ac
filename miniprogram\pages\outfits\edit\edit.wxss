/* pages/outfits/edit/edit.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-container.small {
  height: auto;
  padding: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-container.small .loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 3px;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 顶部工具栏 */
.toolbar {
  padding: 12px 16px 12px 0;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  height: 36px;
  width: 100%;
  justify-content: flex-start;
  margin-left: 0;
  padding-left: 0;
}

.category-label {
  font-size: 14px;
  color: #333333;
  margin-right: 8px;
  height: 36px;
  line-height: 36px;
  display: flex;
  align-items: center;
}

/* 添加新样式 */
.toolbar-category-label {
  font-size: 14px;
  color: #333333;
  margin-right: 8px;
  display: flex;
  align-items: center;
  height: 36px;
}

/* 名称输入容器 */
.name-input-container {
  flex: 1;
  margin: 0 16px;
  display: flex;
  align-items: center;
  height: 36px;
}

/* 名称输入框 */
.name-input {
  width: 100%;
  height: 36px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f8f8f8;
}

.save-btn {
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  background-color: #000000;
  color: #ffffff;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
  margin-left: 0;
  transition: all 0.3s ease;
}

.save-btn-disabled {
  background-color: #ccc !important;
  color: #999 !important;
  cursor: not-allowed;
}

.permission-loading {
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  background-color: #f8f8f8;
  color: #999;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
  margin-left: 0;
}

/* 分类选择器 */
.category-selector {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.category-label {
  font-size: 15px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 分类选择容器 */
.category-select-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  background-color: #ffffff;
}

.category-picker {
  width: 120px;
}

.picker-value {
  height: 36px;
  padding: 0 10px;
  font-size: 14px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: transparent;
}

.picker-arrow {
  font-size: 12px;
  color: #999999;
}

/* 新增分类按钮 */
.add-category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 48px;
  background-color: #000000;
  color: #ffffff;
  border-radius: 8px;
  margin-left: 12px;
  font-size: 14px;
}

.add-icon-small {
  font-size: 18px;
  margin-right: 4px;
  color: #333333;
}

.add-text {
  font-size: 14px;
  color: #333333;
}

/* 画布容器 */
.canvas-container {
  flex: 1;
  position: relative;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

.movable-area {
  position: absolute;
  top: 0;
  left: 0;
}

/* 可移动物品 */
.movable-item {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s;
}

.movable-item.active {
  outline: 2px dashed #000000;
}

/* 添加删除按钮样式 */
.item-delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
}

.delete-icon {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
}

.item-delete-btn:active {
  opacity: 1;
}

.item-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
  transition: transform 0.2s;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 空画布状态 */
.empty-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  width: 80%;
}

/* 控制面板 */
.control-panel {
  padding: 16px;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
}

.panel-close {
  font-size: 16px;
  padding: 4px;
  color: #999999;
}

.control-group {
  margin-bottom: 16px;
}

.control-label {
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
}

.control-slider {
  width: 100%;
}

.control-actions {
  display: flex;
  margin-top: 20px;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
}

.action-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.action-text {
  font-size: 12px;
}

.action-button.delete .action-icon,
.action-button.delete .action-text {
  color: #ff3b30;
}

/* 添加按钮 */
.add-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background-color: #000000;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  transition: bottom 0.3s ease;
}

/* 添加新样式 - 当控制面板显示时上移按钮位置 */
.add-btn.above-panel {
  bottom: 280px; /* 调整到控制面板上方 */
  transition: bottom 0.3s ease;
}

.add-icon {
  color: #ffffff;
  font-size: 32px;
}

/* 衣物选择器 */
.clothes-selector {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70vh;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.clothes-selector.show {
  transform: translateY(0);
}

.selector-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.selector-title {
  font-size: 18px;
  font-weight: 500;
}

.selector-upload {
  font-size: 14px;
  color: #3984ff;
  margin-left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f0f7ff;
}

.selector-close {
  font-size: 18px;
  padding: 4px;
  color: #999999;
}

/* 筛选条件 */
.filter-container {
  border-bottom: 1px solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
  padding: 12px 0;
}

.filter-tabs {
  display: inline-flex;
  padding: 0 16px;
}

.filter-tab {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  background-color: #f5f5f5;
  color: #666666;
}

.filter-tab.active {
  background-color: #000000;
  color: #ffffff;
}

/* 衣物列表 */
.clothes-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  align-items: center; /* 水平居中 */
  box-sizing: border-box; /* 确保padding不影响布局 */
}

.clothes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  width: 100%; /* 设置宽度为100% */
  max-width: 580px; /* 限制最大宽度，避免在大屏幕上过宽 */
  margin: 0 auto; /* 添加水平居中 */
}

.clothes-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box; /* 确保边框和内边距不增加宽度 */
}

.clothes-img {
  width: 100%;
  height: 120px;
  object-fit: contain;
  background-color: #f5f5f5;
}

.clothes-name {
  font-size: 12px;
  padding: 8px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  box-sizing: border-box;
}

/* 空状态 */
.empty-clothes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

/* 分类选择器样式 */
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.picker-value {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.add-category-btn {
  font-size: 28rpx;
  color: #3984ff;
  margin-left: 20rpx;
  padding: 10rpx 20rpx;
}

/* 创建分类弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.modal-content {
  width: 85%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input, .form-textarea {
  width: 100%;
  background: #f8f8f8;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  height: 200rpx;
}

.character-count {
  text-align: right;
  font-size: 24rpx;
  color: #888;
  margin-top: 10rpx;
}

.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin: 0 10rpx;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-submit {
  background: #3984ff;
  color: #fff;
}

/* 更新为 */

/* 分类选择器样式 */
.category-selector {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
}

.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-picker {
  flex: 1;
}

.picker-value {
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999999;
}

/* 新增分类按钮 */
.add-category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 48px;
  background-color: #000000;
  color: #ffffff;
  border-radius: 8px;
  margin-left: 12px;
  font-size: 14px;
}

/* 添加下拉菜单容器和项目样式 */
.dropdown-container {
  position: relative;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 120px;
  height: 36px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-sizing: border-box;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 140px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 101;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 10px 12px;
  font-size: 14px;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background-color: #f5f5f5;
}

.dropdown-item.create-new {
  color: #333333;
  font-weight: normal;
}

/* 对齐线样式 */
.alignment-line {
  position: absolute;
  background-color: #333333; /* 灰黑色 */
  z-index: 999; /* 确保显示在衣物上方 */
  opacity: 0.7; /* 半透明效果 */
  pointer-events: none; /* 防止干扰点击事件 */
}

.alignment-line.horizontal {
  left: 0;
  width: 100%;
  height: 1px; /* 水平线高度为1px */
  border-top: 1px dashed #333333; /* 虚线样式 */
  background-color: transparent; /* 透明背景，只显示虚线 */
}

.alignment-line.horizontal.bottom {
  border-bottom: 1px dashed #333333; /* 底部边界使用bottom border */
  border-top: none;
  bottom: 0;
}

.alignment-line.vertical {
  top: 0;
  width: 1px; /* 垂直线宽度为1px */
  height: 100%;
  border-left: 1px dashed #333333; /* 虚线样式 */
  background-color: transparent; /* 透明背景，只显示虚线 */
}

.alignment-line.vertical.right {
  border-right: 1px dashed #333333; /* 右边界使用right border */
  border-left: none;
  right: 0;
}

/* 新增：数据源切换按钮样式 */
.data-source-btn {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 36rpx;
  font-size: 30rpx;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
  margin-right: 20rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.data-source-btn:active {
  background-color: #f5f5f5;
}

.data-source-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.data-source-dropdown-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 3rpx solid #666;
  border-bottom: 3rpx solid #666;
  transform: rotate(45deg);
  margin-left: 16rpx;
  margin-top: -4rpx;
}

/* 衣物图片容器 */
.clothes-img-container {
  position: relative;
  width: 100%;
  height: 120px;
}

/* 数据源标识样式 */
.data-source-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background-color: #4CAF50;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  z-index: 10;
}

.badge-text {
  font-size: 18rpx;
  color: white;
  font-weight: 500;
}

/* 创建者信息样式 */
.creator-info {
  margin-top: 8rpx;
  text-align: center;
}

.creator-text {
  font-size: 20rpx;
  color: #999;
}

/* 数据源选择弹出框样式 */
.data-source-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.data-source-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 32rpx 32rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1001;
}

.data-source-popup-show {
  transform: translateY(0);
}

.data-source-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-source-popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.data-source-popup-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-source-popup-content {
  padding: 20rpx 40rpx 60rpx;
}

.data-source-popup-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.data-source-popup-item:last-child {
  margin-bottom: 0;
}

.data-source-popup-item.active {
  background-color: #e8f5e8;
  border: 2rpx solid #4CAF50;
}

.data-source-popup-item:not(.active):active {
  background-color: #f8f8f8;
}

.data-source-popup-item .data-source-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.data-source-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}