<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取请求的JSON数据
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的请求数据'
    ]);
    exit;
}

// 验证必填字段
if (!isset($data['name']) || empty(trim($data['name']))) {
    echo json_encode([
        'error' => true,
        'msg' => '穿搭名称不能为空'
    ]);
    exit;
}

// 验证items数组格式
if (!isset($data['items']) || !is_array($data['items'])) {
    $data['items'] = []; // 如果没有提供或格式不正确，设为空数组
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 准备穿搭数据
    $name = trim($data['name']);
    $description = isset($data['description']) ? trim($data['description']) : null;
    $thumbnailUrl = isset($data['thumbnail']) ? $data['thumbnail'] : null;
    $categoryId = isset($data['category_id']) ? intval($data['category_id']) : null;
    
    // 验证分类ID是否存在且属于当前用户
    if ($categoryId) {
        $stmt = $conn->prepare("SELECT id FROM outfit_categories WHERE id = :id AND user_id = :user_id");
        $stmt->execute([
            'id' => $categoryId,
            'user_id' => $userId
        ]);
        
        if ($stmt->rowCount() === 0) {
            // 分类不存在或不属于当前用户，使用默认分类
            $categoryId = null;
        }
    }
    
    // 如果没有指定分类或分类无效，尝试获取默认分类
    if (!$categoryId) {
        $stmt = $conn->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND is_default = 1");
        $stmt->execute(['user_id' => $userId]);
        $defaultCategory = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($defaultCategory) {
            $categoryId = $defaultCategory['id'];
        } else {
            // 如果没有默认分类，创建一个
            $stmt = $conn->prepare("
                INSERT INTO outfit_categories (user_id, name, description, is_default, created_at)
                VALUES (:user_id, '默认分类', '自动创建的默认分类', 1, NOW())
            ");
            $stmt->execute(['user_id' => $userId]);
            $categoryId = $conn->lastInsertId();
        }
    }
    
    // 构建outfit_data
    $outfitData = [
        'items' => $data['items']
    ];
    
    // 将outfit_data转为JSON
    $outfitDataJson = json_encode($outfitData);
    
    // 检查是否为更新操作
    $isUpdate = false;
    $outfitId = null;
    
    if (isset($data['id']) && !empty($data['id'])) {
        $outfitId = $data['id'];
        
        // 检查穿搭是否存在且属于当前用户
        $checkStmt = $conn->prepare("SELECT id FROM outfits WHERE id = :id AND user_id = :user_id");
        $checkStmt->execute([
            'id' => $outfitId,
            'user_id' => $userId
        ]);
        
        if ($checkStmt->rowCount() > 0) {
            $isUpdate = true;
        } else {
            // 穿搭不存在或不属于当前用户，创建新穿搭
            $outfitId = null;
        }
    }
    
    if ($isUpdate) {
        // 更新现有穿搭
        $stmt = $conn->prepare("
            UPDATE outfits 
            SET name = :name, 
                description = :description, 
                thumbnail_url = :thumbnail_url, 
                category_id = :category_id,
                outfit_data = :outfit_data,
                updated_at = NOW()
            WHERE id = :id AND user_id = :user_id
        ");
        
        $stmt->execute([
            'name' => $name,
            'description' => $description,
            'thumbnail_url' => $thumbnailUrl,
            'category_id' => $categoryId,
            'outfit_data' => $outfitDataJson,
            'id' => $outfitId,
            'user_id' => $userId
        ]);
        
        // 获取分类名称
        $stmt = $conn->prepare("SELECT name FROM outfit_categories WHERE id = :id");
        $stmt->execute(['id' => $categoryId]);
        $category = $stmt->fetch(PDO::FETCH_ASSOC);
        $categoryName = $category ? $category['name'] : null;
        
        // 返回结果
        echo json_encode([
            'success' => true,
            'msg' => '穿搭更新成功',
            'data' => [
                'id' => $outfitId,
                'name' => $name,
                'description' => $description,
                'thumbnail' => $thumbnailUrl,
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'items' => $data['items'],
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        // 创建新穿搭
        $stmt = $conn->prepare("
            INSERT INTO outfits (user_id, name, description, thumbnail_url, category_id, outfit_data, created_at, updated_at)
            VALUES (:user_id, :name, :description, :thumbnail_url, :category_id, :outfit_data, NOW(), NOW())
        ");
        
        $stmt->execute([
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'thumbnail_url' => $thumbnailUrl,
            'category_id' => $categoryId,
            'outfit_data' => $outfitDataJson
        ]);
        
        $newOutfitId = $conn->lastInsertId();
        
        // 获取分类名称
        $stmt = $conn->prepare("SELECT name FROM outfit_categories WHERE id = :id");
        $stmt->execute(['id' => $categoryId]);
        $category = $stmt->fetch(PDO::FETCH_ASSOC);
        $categoryName = $category ? $category['name'] : null;
        
        // 返回结果
        echo json_encode([
            'success' => true,
            'msg' => '穿搭创建成功',
            'data' => [
                'id' => $newOutfitId,
                'name' => $name,
                'description' => $description,
                'thumbnail' => $thumbnailUrl,
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'items' => $data['items'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '保存穿搭失败: ' . $e->getMessage()
    ]);
} 