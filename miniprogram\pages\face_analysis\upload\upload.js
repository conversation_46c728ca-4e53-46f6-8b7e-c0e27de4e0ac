// pages/face_analysis/upload/upload.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    frontPhotoPath: '', // 正面照片临时路径
    sidePhotoPath: '',  // 侧面照片临时路径
    preferredStyle: '', // 风格偏好
    isUploading: false, // 是否正在上传
    uploadProgress: 0,   // 上传进度（0-100）
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('面容分析页面加载，检查登录状态');
    console.log('useMockUser:', app.globalData.useMockUser);
    console.log('userInfo:', app.globalData.userInfo);
    console.log('token:', wx.getStorageSync('token'));
    
    // 检查用户是否已登录或使用体验账号
    if (!app.globalData.userInfo && !wx.getStorageSync('token') && !app.globalData.useMockUser) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再使用面容分析功能',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/my/my'
            });
          }
        }
      });
    } else if (app.globalData.useMockUser) {
      // 使用体验账号
      console.log('当前使用体验账号，ID为1');
    }
  },
  
  /**
   * 选择正面照片
   */
  chooseFrontPhoto() {
    console.log('选择正面照片');
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'front',
      success: (res) => {
        console.log('选择照片成功:', res.tempFiles[0].tempFilePath);
        this.setData({
          frontPhotoPath: res.tempFiles[0].tempFilePath
        }, () => {
          console.log('设置frontPhotoPath成功:', this.data.frontPhotoPath);
        });
      },
      fail: (err) => {
        console.error('选择照片失败:', err);
      }
    });
  },
  
  /**
   * 选择侧面照片
   */
  chooseSidePhoto() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'side',
      success: (res) => {
        this.setData({
          sidePhotoPath: res.tempFiles[0].tempFilePath
        });
      }
    });
  },
  
  /**
   * 清除正面照片
   */
  clearFrontPhoto(e) {
    console.log('清除正面照片');
    // 阻止事件冒泡，防止触发chooseFrontPhoto
    e && e.stopPropagation();
    this.setData({
      frontPhotoPath: ''
    }, () => {
      console.log('清除frontPhotoPath成功，现在值为:', this.data.frontPhotoPath);
    });
  },
  
  /**
   * 清除侧面照片
   */
  clearSidePhoto(e) {
    // 阻止事件冒泡，防止触发chooseSidePhoto
    e && e.stopPropagation();
    this.setData({
      sidePhotoPath: ''
    });
  },
  
  /**
   * 监听风格偏好输入
   */
  onStyleInput(e) {
    this.setData({
      preferredStyle: e.detail.value
    });
  },
  
  /**
   * 提交分析请求
   */
  submitAnalysis() {
    console.log('开始分析，正面照片路径:', this.data.frontPhotoPath);
    console.log('正面照片路径类型:', typeof this.data.frontPhotoPath);
    console.log('正面照片路径长度:', this.data.frontPhotoPath ? this.data.frontPhotoPath.length : 0);
    
    // 验证是否选择了正面照片
    if (!this.data.frontPhotoPath) {
      wx.showToast({
        title: '请先选择正面照片',
        icon: 'none'
      });
      return;
    }
    
    // 设置上传状态
    this.setData({
      isUploading: true,
      uploadProgress: 0
    });
    
    // 显示加载提示
    wx.showLoading({
      title: '正在处理照片...',
      mask: true
    });
    
    // 获取token
    let token = wx.getStorageSync('token');
    
    // 如果没有token但是使用模拟用户，则创建一个模拟token
    if ((!token || token.length === 0) && app.globalData.useMockUser) {
      console.log('使用体验账号，创建模拟token');
      token = app.globalData.token || 'mock_token_' + Date.now();
    }
    
    if (!token) {
      wx.hideLoading();
      this.setData({ isUploading: false });
      
      wx.showModal({
        title: '提示',
        content: '登录信息已过期，请重新登录',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/my/my'
            });
          }
        }
      });
      return;
    }
    
    console.log('使用token:', token);
    console.log('是否为模拟用户:', app.globalData.useMockUser);
    
    // 转换照片为Base64格式
    Promise.all([
      this.convertImageToBase64(this.data.frontPhotoPath),
      this.data.sidePhotoPath ? this.convertImageToBase64(this.data.sidePhotoPath) : Promise.resolve('')
    ]).then(([frontPhotoBase64, sidePhotoBase64]) => {
      // 更新进度
      this.setData({ uploadProgress: 30 });
      
      // 准备请求数据 - 使用新的Base64直传模式
      const requestData = {
        front_photo: frontPhotoBase64,
        preferred_style: this.data.preferredStyle,
        action: 'analyze'
      };

      // 如果有侧面照片，也添加到请求中
      if (sidePhotoBase64) {
        requestData.side_photo = sidePhotoBase64;
      }

      console.log('开始上传Base64照片数据（直传模式）');
      console.log('正面照片Base64长度:', frontPhotoBase64.length);
      if (sidePhotoBase64) {
        console.log('侧面照片Base64长度:', sidePhotoBase64.length);
      }

      // 发送请求
      wx.request({
        url: `${app.globalData.baseUrl}/face_analysis.php`,
        method: 'POST',
        header: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': token
        },
        data: requestData,
        success: (res) => {
          wx.hideLoading();
          this.setData({ 
            isUploading: false,
            uploadProgress: 100
          });
          
          console.log('上传响应:', res.data);
          
          if (res.data.error) {
            console.error('上传失败:', res.data.msg);
            wx.showToast({
              title: res.data.msg || '分析请求失败',
              icon: 'none'
            });
          } else {
            // 上传成功，跳转到结果页面
            const analysisId = res.data.data.id;
            console.log('上传成功，分析ID:', analysisId);
            wx.navigateTo({
              url: `/pages/face_analysis/result/index?id=${analysisId}`
            });
          }
        },
        fail: (err) => {
          console.error('上传失败:', err);
          wx.hideLoading();
          this.setData({ isUploading: false });
          
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }
      });
      
      // 模拟上传进度
      let progress = 30;
      const progressInterval = setInterval(() => {
        progress += 5;
        if (progress >= 90) {
          clearInterval(progressInterval);
          return;
        }
        this.setData({ uploadProgress: progress });
      }, 500);
    }).catch(error => {
      console.error('处理照片失败:', error);
      wx.hideLoading();
      this.setData({ isUploading: false });
      
      wx.showToast({
        title: '处理照片失败，请重试',
        icon: 'none'
      });
    });
  },
  
  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    return {
      title: '面容分析 - 次元衣帽间',
      path: '/pages/face_analysis/index/index',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  },

  /**
   * 将本地图片转换为Base64格式
   */
  convertImageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      console.log('开始转换图片为Base64:', filePath);
      try {
        // 读取文件内容
        const fileManager = wx.getFileSystemManager();
        const fileData = fileManager.readFileSync(filePath);
        
        // 转换为Base64
        const base64 = wx.arrayBufferToBase64(fileData);
        
        // 获取文件类型
        let fileType = 'jpeg'; // 默认类型
        if (filePath.endsWith('.png')) {
          fileType = 'png';
        } else if (filePath.endsWith('.gif')) {
          fileType = 'gif';
        } else if (filePath.endsWith('.webp')) {
          fileType = 'webp';
        }
        
        // 组合完整的Base64字符串（带前缀）
        const base64Data = `data:image/${fileType};base64,${base64}`;
        console.log('图片转换为Base64成功，长度:', base64Data.length);
        
        resolve(base64Data);
      } catch (error) {
        console.error('转换图片为Base64失败:', error);
        reject(error);
      }
    });
  }
}) 