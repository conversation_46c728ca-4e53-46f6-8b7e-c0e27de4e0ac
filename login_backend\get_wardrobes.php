<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 响应方法
$method = $_SERVER['REQUEST_METHOD'];
if ($method !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
    $offset = ($page - 1) * $per_page;

    // 新增：圈子数据相关参数（向后兼容）
    $includeCircleData = isset($_GET['include_circle_data']) ? $_GET['include_circle_data'] === 'true' : false;
    $dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'personal'; // personal, shared, all

    // 添加详细的调试日志
    error_log("衣橱API请求 - 用户ID: $user_id, 包含圈子数据: " . ($includeCircleData ? 'true' : 'false') . ", 数据源: $dataSource");

    // 构建查询（向后兼容）
    if ($includeCircleData && $dataSource !== 'personal') {
        // 新功能：包含圈子数据的查询
        if ($dataSource === 'shared') {
            // 查询用户所在圈子的共享衣橱
            $stmt = $pdo->prepare("
                SELECT DISTINCT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at,
                       u.nickname as creator_nickname,
                       CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM wardrobes w
                LEFT JOIN users u ON w.user_id = u.id
                WHERE w.circle_id IS NOT NULL AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC
                LIMIT :limit OFFSET :offset
            ");
        } else { // $dataSource === 'all'
            // 查询个人衣橱 + 圈子共享衣橱
            $stmt = $pdo->prepare("
                SELECT DISTINCT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at,
                       u.nickname as creator_nickname,
                       CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM wardrobes w
                LEFT JOIN users u ON w.user_id = u.id
                WHERE (w.user_id = :user_id AND w.circle_id IS NULL) OR
                      (w.circle_id IS NOT NULL AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
                ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC
                LIMIT :limit OFFSET :offset
            ");
        }
    } else {
        // 原有逻辑：只查询个人衣橱（保持向后兼容）
        $stmt = $pdo->prepare("
            SELECT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at
            FROM wardrobes w
            WHERE w.user_id = :user_id
            ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC
            LIMIT :limit OFFSET :offset
        ");
    }
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $per_page, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $wardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取每个衣橱的衣物数量
    foreach ($wardrobes as &$wardrobe) {
        if ($includeCircleData && isset($wardrobe['data_source']) && $wardrobe['data_source'] === 'shared') {
            // 对于共享衣橱，统计圈子中的衣物数量
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as clothes_count
                FROM clothes
                WHERE wardrobe_id = :wardrobe_id AND circle_id IS NOT NULL
            ");
        } else {
            // 对于个人衣橱，统计用户自己的衣物数量
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as clothes_count
                FROM clothes
                WHERE wardrobe_id = :wardrobe_id AND user_id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        }
        $stmt->bindParam(':wardrobe_id', $wardrobe['id'], PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $wardrobe['clothes_count'] = (int)$result['clothes_count'];

        // 为向后兼容，如果没有数据源字段，添加默认值
        if (!isset($wardrobe['data_source'])) {
            $wardrobe['data_source'] = 'personal';
            $wardrobe['creator_nickname'] = null;
        }
    }
    
    // 获取总衣橱数量（用于分页）
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM wardrobes WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $total = (int)$stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 计算总页数
    $total_pages = ceil($total / $per_page);
    
    // 返回成功响应
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $wardrobes,
        'pagination' => [
            'total' => $total,
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => $total_pages
        ],
        'meta' => [
            'include_circle_data' => $includeCircleData,
            'data_source' => $dataSource,
            'total_count' => count($wardrobes)
        ]
    ]);
    
} catch (PDOException $e) {
    // 记录错误日志
    error_log("Database error in get_wardrobes.php: " . $e->getMessage());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode(['error' => 'Database error', 'message' => 'An error occurred while retrieving wardrobes']);
} catch (Exception $e) {
    // 记录错误日志
    error_log("General error in get_wardrobes.php: " . $e->getMessage());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode(['error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 