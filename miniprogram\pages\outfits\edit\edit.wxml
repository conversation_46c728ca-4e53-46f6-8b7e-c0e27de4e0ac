<view class="container" bindtap="hideDropdown">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 编辑区域 -->
  <block wx:if="{{!loading && outfit}}">
    <!-- 顶部工具栏 -->
    <view class="toolbar">
      <view class="toolbar-left">
        <text class="toolbar-category-label">分类</text>
        <view class="dropdown-container" catchtap="stopPropagation">
          <view class="picker-display" bindtap="toggleDropdown">
            <text>{{categoryName || '分类名称'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
          
          <!-- 真实的下拉菜单 -->
          <view class="dropdown-menu" wx:if="{{showDropdown}}">
            <view 
              wx:for="{{categories}}" 
              wx:key="id" 
              class="dropdown-item"
              data-index="{{index}}"
              bindtap="onCategorySelect">
              {{item.name}}
            </view>
            <view class="dropdown-item create-new" bindtap="goToAddCategory">新建分类</view>
          </view>
        </view>
        
        <!-- 新增：穿搭名称输入框 -->
        <view class="name-input-container" catchtap="stopPropagation">
          <input 
            class="name-input" 
            placeholder="穿搭名称" 
            maxlength="20" 
            bindinput="onNameInput"
            value="{{outfit.name}}"
          />
        </view>
        
        <view class="save-btn" bindtap="saveOutfit">保存</view>
      </view>
    </view>
    
    <!-- 新增：创建分类弹窗 -->
    <view class="modal-mask" wx:if="{{showCategoryModal}}" bindtap="closeCategoryModal">
      <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
          <text class="modal-title">创建新分类</text>
          <view class="modal-close" bindtap="closeCategoryModal">×</view>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <view class="form-label">分类名称</view>
            <input class="form-input" value="{{newCategory.name}}" bindinput="onCategoryNameInput" 
                   placeholder="请输入分类名称" maxlength="20" />
            <view class="character-count">{{categoryNameLength}}/20</view>
          </view>
          
          <view class="form-group">
            <view class="form-label">分类描述</view>
            <textarea class="form-textarea" value="{{newCategory.description}}" bindinput="onCategoryDescInput"
                      placeholder="请输入分类描述" maxlength="100" />
            <view class="character-count">{{categoryDescLength}}/100</view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn-cancel" bindtap="closeCategoryModal">取消</button>
          <button class="btn-submit" bindtap="createCategory" loading="{{submittingCategory}}">确认</button>
        </view>
      </view>
    </view>
    
    <!-- 穿搭画布 -->
    <view class="canvas-container" style="height: {{canvasHeight}}px;">
      <!-- 使用单一的movable-area容器 -->
      <movable-area class="movable-area" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;">
        <!-- 添加对齐线 -->
        <view class="alignment-line horizontal" wx:if="{{showHorizontalLine && !isBottomBorder}}" style="top: {{horizontalLinePosition}}px;"></view>
        <view class="alignment-line horizontal bottom" wx:if="{{showHorizontalLine && isBottomBorder}}" style="bottom: 0;"></view>
        <view class="alignment-line vertical" wx:if="{{showVerticalLine && !isRightBorder}}" style="left: {{verticalLinePosition}}px;"></view>
        <view class="alignment-line vertical right" wx:if="{{showVerticalLine && isRightBorder}}" style="right: 0;"></view>
        
        <!-- 循环渲染每个衣物项为movable-view -->
        <movable-view 
          wx:for="{{outfit.items}}" 
          wx:key="clothing_id"
          class="movable-item {{activeItemIndex === index ? 'active' : ''}}"
          direction="all"
          x="{{item.position.x}}"
          y="{{item.position.y}}"
          style="width: {{item.size.width}}px; height: {{item.size.height}}px; z-index: {{item.z_index}};"
          data-index="{{index}}"
          bindtap="selectItem"
          bindchange="onItemMove"
          bindtouchend="onItemMoveEnd"
          out-of-bounds="true"
          damping="0"
          friction="0"
          inertia="{{false}}"
          animation="{{false}}"
          disabled="{{false}}">
          <view class="item-delete-btn" catchtap="deleteItem" data-index="{{index}}" wx:if="{{activeItemIndex === index}}">
            <text class="delete-icon">✕</text>
          </view>
          <view class="item-wrapper" style="width: 100%; height: 100%; transform: rotate({{item.rotation}}deg);">
            <image src="{{item.clothing_data.image_url}}" mode="aspectFit" class="item-image"></image>
          </view>
        </movable-view>
      </movable-area>
      
      <!-- 空画布提示 -->
      <view class="empty-canvas" wx:if="{{outfit.items.length === 0}}">
        <text class="empty-icon">👔</text>
        <text class="empty-text">点击下方添加按钮开始创建穿搭</text>
      </view>
    </view>
    
    <!-- 控制面板 -->
    <view class="control-panel" wx:if="{{activeItemIndex >= 0}}">
      <view class="panel-header">
        <text class="panel-title">衣物调整</text>
        <text class="panel-close" bindtap="selectItem" data-index="-1">✕</text>
      </view>
      
      <view class="control-group">
        <text class="control-label">缩放</text>
        <slider class="control-slider" min="0.5" max="2" step="0.1" value="{{scaleValue}}" bindchange="scaleItem" show-value></slider>
      </view>
      
      <view class="control-group">
        <text class="control-label">旋转</text>
        <slider class="control-slider" min="0" max="359" step="1" value="{{rotateValue}}" bindchange="rotateItem" show-value></slider>
      </view>
      
      <view class="control-actions">
        <view class="action-button" bindtap="sendBackward">
          <text class="action-icon">↓</text>
          <text class="action-text">下移</text>
        </view>
        <view class="action-button" bindtap="bringForward">
          <text class="action-icon">↑</text>
          <text class="action-text">上移</text>
        </view>
      </view>
    </view>
    
    <!-- 添加衣物按钮 - 根据控制面板状态调整位置 -->
    <view class="add-btn {{activeItemIndex >= 0 ? 'above-panel' : ''}}" bindtap="showClothesSelector">
      <text class="add-icon">+</text>
    </view>
  </block>
  
  <!-- 衣物选择器弹窗 -->
  <view class="clothes-selector {{showClothesSelector ? 'show' : ''}}" catchtouchmove="true">
    <view class="selector-header">
      <text class="selector-title">选择衣物</text>
      <text class="selector-upload" bindtap="showUploadOptions">手动上传</text>
      <view style="flex: 1;"></view>
      <text class="selector-close" bindtap="hideClothesSelector">✕</text>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-container">
      <!-- 衣橱筛选 -->
      <scroll-view scroll-x class="filter-scroll">
        <view class="filter-tabs">
          <view 
            wx:for="{{wardrobes}}" 
            wx:key="id" 
            class="filter-tab {{selectedWardrobe == item.id ? 'active' : ''}}"
            bindtap="changeWardrobe"
            data-id="{{item.id}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
      
      <!-- 分类筛选 -->
      <scroll-view scroll-x class="filter-scroll">
        <view class="filter-tabs">
          <view 
            wx:for="{{clothingCategories}}" 
            wx:key="code"
            class="filter-tab {{selectedCategory == item.code ? 'active' : ''}}" 
            bindtap="changeCategory" 
            data-item="{{item}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 衣物列表 -->
    <scroll-view scroll-y class="clothes-list">
      <view class="clothes-grid">
        <view 
          wx:for="{{clothingList}}" 
          wx:key="id"
          class="clothes-item"
          bindtap="selectClothing"
          data-id="{{item.id}}">
          <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
          <view class="clothes-name">{{item.name || '未命名衣物'}}</view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-clothes" wx:if="{{clothingList.length === 0 && !loadingClothes}}">
        <text class="empty-icon">👕</text>
        <text class="empty-text">没有找到衣物</text>
      </view>
      
      <!-- 加载中 -->
      <view class="loading-container small" wx:if="{{loadingClothes}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </scroll-view>
  </view>
</view> 